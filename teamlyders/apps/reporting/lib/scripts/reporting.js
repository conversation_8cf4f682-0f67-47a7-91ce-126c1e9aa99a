TL.Ready(["Inputs", "Downloader"], function () {
	TL.AddScript("resource?src=scripts/reporting-shared.js", function () {
		// Define global state
		const State = {
			UniqueValuesThreshold: 20,
			CurrentCategory: null,
			ReportData: {},
			FavoriteReports: [],
			ReportCategories: [],
			CurrentReportId: null, // for single report view
			CurrentReportTableData: null, // Raw API data array from FetchReportTableData
			CurrentReportMetadata: null, // Report info and column metadata
			CurrentFilters: {
				search: "", // Global search query
				columnFilters: {}, // Dynamic column filters (key: column, value: filter value)
				advancedFilters: [], // Advanced filter builder rules
			},
			// Pagination state for client-side scroll pagination
			TablePagination: {
				FilteredData: [], // Currently filtered dataset
				RenderedRowCount: 0, // Number of rows currently rendered in DOM
				BatchSize: 50, // Number of rows to render per scroll batch
				IsLoading: false, // Prevent duplicate scroll triggers
				ScrollListenerInitialized: false, // Track if scroll listener is attached
			},
		};

		// Navigation Object To Handle URL And Routing
		const Navigation = {
			GetCategoryFromURL: () => {
				const Category = TL.Location.URLParams("category");
				return Category && State.ReportData[Category] ? Category : null;
			},
			GetReportIdFromURL: () => {
				const Id = TL.Location.URLParams("id");
				return Id ? parseInt(Id, 10) : null;
			},
			UpdateURL: (CategoryKey) => {
				if (!CategoryKey) return;
				TL.Browser.EditURL(`reporting?category=${CategoryKey}`);
			},
			UpdateURLWithReport: (ReportId) => {
				if (!ReportId) return;
				TL.Browser.EditURL(`reporting?id=${ReportId}`);
			},
			GetDefaultCategory: () => {
				// Return the first available category from the fetched data (excluding favorites)
				const Categories = Object.keys(State.ReportData).filter((cat) => cat !== "favorites");
				return Categories.length > 0 ? Categories[0] : "favorites";
			},
		};

		// DOM Elements (resolved at runtime in InitializeReporting to handle scripts loaded before markup)
		const Elements = {
			AppWrapper: null,
			FavoritesList: null,
			FavoritesCount: null,
			CategoryList: null,
			CategoryTitle: null,
			CategoryDescription: null,
			ReportsGrid: null,
			EmptyState: null,
			MainContent: null,
		};

		// Loader helper (mirrors pattern used by the benefits app)
		const Loader = {
			Start: (Container = "#reportingAppContainer") => {
				TL.Loading.Start(Container);
			},
			Stop: (Container = "#reportingAppContainer") => {
				TL.Loading.Stop(Container);
			},
		};

		// Small show/hide helpers (accept selector string, Element, NodeList or Array)
		function ShowElement(Target) {
			if (!Target) return;
			const Apply = (El) => {
				if (!El) return;
				El.classList.remove("hidden");
			};

			if (typeof Target === "string") {
				const Nodes = document.querySelectorAll(Target);
				Nodes.forEach(Apply);
				return;
			}

			if (NodeList.prototype.isPrototypeOf(Target) || Array.isArray(Target)) {
				Target.forEach(Apply);
				return;
			}

			Apply(Target);
		}

		function HideElement(Target) {
			if (!Target) return;
			const Apply = (El) => {
				if (!El) return;
				El.classList.add("hidden");
			};

			if (typeof Target === "string") {
				const Nodes = document.querySelectorAll(Target);
				Nodes.forEach(Apply);
				return;
			}

			if (NodeList.prototype.isPrototypeOf(Target) || Array.isArray(Target)) {
				Target.forEach(Apply);
				return;
			}

			Apply(Target);
		}

		// Initialize The Reporting App
		InitializeReporting();

		/*
		 **
		 ** Initialize Reporting App
		 ** ========================
		 ** Fetch reports, favorites and categories in parallel (non-blocking)
		 ** and perform rendering / event wiring in a final block so the UI
		 ** becomes usable even if one of the agents returns an error.
		 */
		function InitializeReporting() {
			// Start loader and mark app as busy before any async work
			Loader.Start();

			// Resolve DOM elements now that the parser should have created them
			Elements.AppWrapper = document.querySelector(".reporting-app");
			Elements.MainContent = document.querySelector(".main-content");
			Elements.FavoritesList = document.getElementById("favoritesList");
			Elements.FavoritesCount = document.getElementById("favoritesCount");
			Elements.CategoryList = document.getElementById("categoryList");
			Elements.CategoryTitle = document.getElementById("categoryTitle");
			Elements.CategoryDescription = document.getElementById("categoryDescription");
			Elements.ReportsGrid = document.getElementById("reportsGrid");
			Elements.EmptyState = document.getElementById("emptyState");

			// Start fetching reports, favorites and categories in parallel.
			const FetchReportsPromise = GetReports();
			const FetchFavoritesPromise = GetFavorites();
			const FetchReportCategoriesPromise = GetReportCategories();

			// Use Promise.allSettled so we can handle each result and still
			// proceed to render the UI regardless of individual failures.
			Promise.allSettled([FetchReportsPromise, FetchFavoritesPromise, FetchReportCategoriesPromise])
				.then((results) => {
					const [reportsRes, favoritesRes, categoriesRes] = results;

					// Report categories (process first so they're available for reports processing)
					if (categoriesRes.status === "fulfilled") {
						TL.DebugLog("Report categories retrieved:", categoriesRes.value);
						State.ReportCategories = categoriesRes.value || [];
					} else if (categoriesRes) {
						TL.Notify.Banner("Error", "Failed to retrieve report categories");
						TL.DebugLog("GetReportCategories failed:", categoriesRes.reason);
						State.ReportCategories = [];
					}

					// Reports (process after categories are loaded)
					if (reportsRes.status === "fulfilled") {
						try {
							State.ReportData = ProcessReportsData(reportsRes.value || {});
							TL.DebugLog("Reports retrieved:", reportsRes.value);
						} catch (err) {
							TL.DebugLog("Error processing reports data:", err);
							State.ReportData = ProcessReportsData({});
						}
					} else {
						TL.DebugLog("GetReports failed:", reportsRes.reason);
						State.ReportData = ProcessReportsData({});
					}

					// Favorites
					if (favoritesRes.status === "fulfilled") {
						const ServerFavorites = favoritesRes.value;
						if (Array.isArray(ServerFavorites) && ServerFavorites.length > 0) {
							// Server returns full report rows; store just the IDs for consistency
							State.FavoriteReports = ServerFavorites.map((r) => r.ID);
						} else {
							State.FavoriteReports = [];
						}
					} else {
						TL.DebugLog("GetFavorites failed:", favoritesRes.reason);
						State.FavoriteReports = [];
					}

					// Determine initial category after report data is available
					let InitialCategory = Navigation.GetCategoryFromURL();
					const RequestedReportId = Navigation.GetReportIdFromURL();
					if (!InitialCategory || !State.ReportData[InitialCategory]) {
						InitialCategory = Navigation.GetDefaultCategory();
					}
					State.CurrentCategory = InitialCategory;
					State.CurrentReportId = RequestedReportId;
				})
				.finally(() => {
					// Render and wire UI after attempts to fetch data
					RenderCategoryList();
					UpdateFavoritesData();
					UpdateFavoritesCount();

					// Reveal category list now that it's populated
					if (Elements.CategoryList) ShowElement(Elements.CategoryList);

					// Set Up Initial State
					SetActiveCategory(State.CurrentCategory, true); // Skip URL Update On First Load
					if (State.CurrentReportId) {
						Loader.Start();
						ShowReportView(State.CurrentReportId);
					} else {
						LoadCategory(State.CurrentCategory);
					}
					InitializeEventListeners();

					// Stop loading and show app
					Loader.Stop();
					if (Elements.AppWrapper) {
						ShowElement(Elements.MainContent);
						ShowElement(Elements.AppWrapper);
					}
				});
		}

		/*
		 **
		 ** Initialize Event Listeners
		 ** ==========================
		 */
		function InitializeEventListeners() {
			// Setup Favorites List Event Listeners
			if (Elements.FavoritesList) {
				const FavoritesItems = Elements.FavoritesList.querySelectorAll(".category-item");
				FavoritesItems.forEach((Item) => {
					Item.addEventListener("click", function () {
						const CategoryKey = this.dataset.category;
						HandleCategoryClick(CategoryKey);
					});
				});
			}

			// Setup Main Category List Event Listeners (for dynamically created categories)
			AttachCategoryEventListeners();

			// Initialize mobile sidebar behavior (toggle + auto-close on selection)
			InitializeMobileSidebar();
		}

		/*
		 **
		 ** Initialize Mobile Sidebar Toggle
		 ** =================================
		 */
		function InitializeMobileSidebar() {
			const Toggle = document.getElementById("sidebarToggle");
			const Close = document.getElementById("sidebarClose");
			const Sidebar = document.querySelector(".sidebar");
			const Backdrop = document.getElementById("sidebarBackdrop");
			const CategoryItems = Elements.CategoryList ? Elements.CategoryList.querySelectorAll(".category-item") : document.querySelectorAll(".category-item");

			if (!Toggle || !Sidebar || !Backdrop) return;

			const OpenSidebar = () => {
				Sidebar.classList.add("open");
				Backdrop.classList.add("show");
				Toggle.setAttribute("aria-expanded", "true");
				Backdrop.setAttribute("aria-hidden", "false");
			};

			const CloseSidebar = () => {
				Sidebar.classList.remove("open");
				Backdrop.classList.remove("show");
				Toggle.setAttribute("aria-expanded", "false");
				Backdrop.setAttribute("aria-hidden", "true");
			};

			Toggle.addEventListener("click", (e) => {
				e.stopPropagation();
				if (Sidebar.classList.contains("open")) CloseSidebar();
				else OpenSidebar();
			});

			// Close button event listener
			if (Close) {
				Close.addEventListener("click", (e) => {
					e.stopPropagation();
					CloseSidebar();
				});
			}

			Backdrop.addEventListener("click", CloseSidebar);

			// Close sidebar when a category is clicked (mobile)
			CategoryItems.forEach((Item) => {
				Item.addEventListener("click", () => {
					if (window.innerWidth <= 900) CloseSidebar();
				});
			});
		}

		/*
		 **
		 ** Process Reports Data From API
		 ** ============================
		 */
		function ProcessReportsData(ApiData) {
			TL.DebugLog("Processing API Data:", ApiData);

			// Initialize with favorites category
			const ProcessedData = {
				favorites: {
					Title: "Favorites",
					Description: "Your favorite reports for quick access",
					Reports: [],
				},
			};

			// Helper function to get category info from ReportCategories
			const GetCategoryInfo = (CategoryKey) => {
				const CategoryData = State.ReportCategories.find((cat) => cat.Name && cat.Name.toLowerCase() === CategoryKey.toLowerCase());

				if (CategoryData) {
					return {
						Title: CategoryData.Name,
						Description: CategoryData.Description || `${CategoryData.Name} reports and analytics`,
					};
				}

				// Fallback if category not found in ReportCategories
				const CategoryName = CategoryKey.charAt(0).toUpperCase() + CategoryKey.slice(1).toLowerCase();
				return {
					Title: CategoryName,
					Description: `${CategoryName} reports and analytics`,
				};
			};

			// Process each category from the API
			Object.keys(ApiData).forEach((CategoryKey) => {
				const CategoryReports = ApiData[CategoryKey];
				if (!Array.isArray(CategoryReports) || CategoryReports.length === 0) return;

				// Get category info from ReportCategories or use fallback
				const CategoryInfo = GetCategoryInfo(CategoryKey);

				ProcessedData[CategoryKey.toLowerCase()] = {
					Title: CategoryInfo.Title,
					Description: CategoryInfo.Description,
					Reports: CategoryReports.map((Report) => ({
						ID: Report.ID,
						Name: Report.Name,
						DisplayName: Report["Display Name"] || Report.Name,
						Description: Report.Description || "No description available",
						Icon: GetReportIcon(Report.Icon || "📊"),
						Category: Report.Category,
						Format: Report.Format,
						Version: Report.Version,
						Popup: Report.Popup,
						ViewInline: Report.ViewInline,
					})),
				};
			});

			return ProcessedData;
		}

		/*
		 **
		 ** Get Report Icon
		 ** ==============
		 */
		function GetReportIcon(IconName) {
			const IconMap = {
				building: "🏢",
				alert: "⚠️",
				contact: "�",
				"people-group": "👥",
				default: "📊",
			};
			return IconMap[IconName] || IconMap.default;
		}

		/*
		 **
		 ** Render Category List
		 ** ===================
		 */
		function RenderCategoryList() {
			const CategoryList = Elements.CategoryList;
			if (!CategoryList) return;

			// Clear existing categories (except favorites which is handled separately)
			CategoryList.innerHTML = "";

			// Get categories excluding favorites
			const Categories = Object.keys(State.ReportData).filter((key) => key !== "favorites");

			// Render each category
			Categories.forEach((CategoryKey) => {
				const Category = State.ReportData[CategoryKey];
				const ReportCount = Category.Reports.length;

				const TemplateData = {
					Category: CategoryKey,
					Name: Category.Title,
					Count: ReportCount,
					ActiveClass: CategoryKey === State.CurrentCategory ? "active" : "",
				};

				const Template = TL.Template("Category-Item");
				if (Template) {
					const FilledTemplate = TL.FillTemplate(Template, TemplateData);
					const TempDiv = document.createElement("div");
					TempDiv.innerHTML = FilledTemplate;
					const CategoryItem = TempDiv.firstElementChild;

					if (CategoryItem) {
						CategoryList.appendChild(CategoryItem);
					}
				}
			});

			// Re-attach event listeners for new category items
			AttachCategoryEventListeners();
		}
		/*
		 **
		 ** Attach Category Event Listeners
		 ** ==============================
		 */
		function AttachCategoryEventListeners() {
			// Setup Main Category List Event Listeners
			if (Elements.CategoryList) {
				const CategoryItems = Elements.CategoryList.querySelectorAll(".category-item");
				CategoryItems.forEach((Item) => {
					Item.addEventListener("click", function () {
						const CategoryKey = this.dataset.category;
						HandleCategoryClick(CategoryKey);
					});
				});
			}
		}

		/*
		 **
		 ** Handle Category Click
		 ** ====================
		 */
		function HandleCategoryClick(CategoryKey) {
			if (!CategoryKey || CategoryKey === State.CurrentCategory) return;

			State.CurrentCategory = CategoryKey;
			SetActiveCategory(CategoryKey);
			LoadCategory(CategoryKey);
		}

		/*
		 **
		 ** Set Active Category Visual State
		 ** ===============================
		 */
		function SetActiveCategory(CategoryKey, SkipUrl = false) {
			if (!CategoryKey) return;

			// Remove Active State From All Category Items In Both Lists
			const AllCategoryItems = [...(Elements.FavoritesList?.querySelectorAll(".category-item") || []), ...(Elements.CategoryList?.querySelectorAll(".category-item") || [])];

			// Remove Active From All Items First
			AllCategoryItems.forEach((Item) => {
				Item.classList.remove("active");
			});

			// Add Active State To The Matching Category
			const TargetItem = AllCategoryItems.find((Item) => Item.dataset.category === CategoryKey);
			if (TargetItem) {
				TargetItem.classList.add("active");
			}

			// Update URL If Not Skipped
			if (!SkipUrl) Navigation.UpdateURL(CategoryKey);
		}

		/*
		 **
		 ** Load Reports For Category
		 ** ========================
		 */
		function LoadCategory(CategoryKey) {
			TL.DebugLog("Loading Category:", CategoryKey);

			const Category = State.ReportData[CategoryKey];
			if (!Category) {
				ShowEmptyState();
				return;
			}

			// Update Page Header
			UpdateCategoryHeader(Category);

			// Clear And Populate Reports
			ClearReportsGrid();

			if (Category.Reports.length === 0) {
				ShowEmptyState();
				return;
			}

			RenderReports(Category.Reports, CategoryKey);
		}

		/*
		 **
		 ** Update Category Header
		 ** =====================
		 */
		function UpdateCategoryHeader(Category) {
			TL.DebugLog("Updating Category Header:", Category);
			Elements.CategoryTitle.textContent = Category.Title;
			Elements.CategoryDescription.textContent = Category.Description;
		}

		/*
		 **
		 ** Clear Reports Grid
		 ** =================
		 */
		function ClearReportsGrid() {
			if (Elements.ReportsGrid) {
				Elements.ReportsGrid.innerHTML = "";
				HideElement(Elements.ReportsGrid);
			}
		}

		/*
		 **
		 ** Render Reports
		 ** =============
		 */
		function RenderReports(Reports, CategoryKey) {
			// Hide Empty State And Show Reports Grid
			if (Elements.EmptyState) HideElement(Elements.EmptyState);
			if (Elements.ReportsGrid) {
				ShowElement(Elements.ReportsGrid);
			}

			// Create And Append Report Cards
			Reports.forEach((Report) => {
				const ReportCard = CreateReportCard(Report, CategoryKey);
				if (Elements.ReportsGrid && ReportCard) {
					Elements.ReportsGrid.appendChild(ReportCard);
				}
			});
		}

		/*
		 **
		 ** Create Report Card Element
		 ** =========================
		 */
		function CreateReportCard(Report, CategoryKey) {
			// Handle "No Description Available" Cases
			const DescriptionClass = Report.Description === "No description available" ? "no-description" : "";

			// Check If Report Is Favorited
			const ReportId = Report.ID;
			const IsFavorited = State.FavoriteReports.includes(ReportId);
			const FavoriteClass = IsFavorited ? "favorited" : "";
			const FavoriteTitle = IsFavorited ? "Remove from favorites" : "Add to favorites";

			// Prepare Template Data
			const TemplateData = {
				Category: CategoryKey,
				Icon: Report.Icon,
				Title: Report.DisplayName || Report.Name,
				Description: Report.Description,
				DescriptionClass: DescriptionClass,
				FavoriteClass: FavoriteClass,
				FavoriteTitle: FavoriteTitle,
			};

			// Get Template And Fill With Data
			const Template = TL.Template("Report-Card");
			if (!Template) {
				TL.DebugLog("Template 'Report-Card' not found");
				return null;
			}

			const FilledTemplate = TL.FillTemplate(Template, TemplateData);
			if (!FilledTemplate) {
				TL.DebugLog("Failed to fill template for report:", Report.Name);
				return null;
			}

			// Create DOM Element
			const TempDiv = document.createElement("div");
			TempDiv.innerHTML = FilledTemplate;
			const Card = TempDiv.firstElementChild;

			if (!Card) {
				TL.DebugLog("Failed to create card element for report:", Report.Name);
				return null;
			}

			// Add Event Listeners
			AttachReportCardEvents(Card, Report, CategoryKey);

			return Card;
		}

		/*
		 **
		 ** Attach Report Card Event Listeners
		 ** =================================
		 */
		function AttachReportCardEvents(Card, Report) {
			if (!Card) {
				TL.DebugLog("Cannot attach events: card is null for report:", Report.Name);
				return;
			}

			// Generate Report Button
			const GenerateBtn = Card.querySelector(".generate-btn");
			if (GenerateBtn) {
				GenerateBtn.addEventListener("click", function () {
					// If the report defines a popup type, open it; otherwise, download full report
					if (Report && Report.Popup) {
						OpenReportPopup(Report);
					} else {
						GenerateReport(Report.Name, Report);
					}
				});
			}

			// Favorite Button
			const FavoriteBtn = Card.querySelector(".favorite-btn");
			if (FavoriteBtn) {
				FavoriteBtn.addEventListener("click", function () {
					ToggleFavorite(FavoriteBtn, Report);
				});
			}

			// View Button
			const ViewBtn = Card.querySelector(".view-btn");
			if (ViewBtn) {
				// Determine if this report allows inline viewing
				const InlineFlag = Report && (Report.ViewInline ?? Report["View Inline"]);
				const InlineFlagText = InlineFlag !== undefined && InlineFlag !== null ? String(InlineFlag).trim().toLowerCase() : "";
				const CanViewInline = InlineFlagText === "" || (InlineFlagText !== "0" && InlineFlagText !== "no" && InlineFlagText !== "false");
				if (!CanViewInline) {
					ViewBtn.disabled = true;
					ViewBtn.setAttribute("aria-disabled", "true");
					ViewBtn.setAttribute("title", "This report can only be downloaded");
					ViewBtn.classList.add("disabled");
				}
				ViewBtn.addEventListener("click", function (e) {
					if (ViewBtn.disabled) {
						e.preventDefault();
						return;
					}
					ShowReportView(Report.ID, Report, { showLoader: true });
				});
			}
		}

		/*
		 ** Build Report Lookup Map
		 */
		function GetReportById(ReportId) {
			if (!ReportId) return null;
			// Search across categories
			for (const CatKey of Object.keys(State.ReportData)) {
				const Cat = State.ReportData[CatKey];
				if (!Cat || !Array.isArray(Cat.Reports)) continue;
				const Match = Cat.Reports.find((r) => r.ID === ReportId);
				if (Match) return Match;
			}
			return null;
		}

		/*
		 ** Show Single Report View
		 */
		function ShowReportView(ReportId, ReportObj, { showLoader = false } = { showLoader: false }) {
			// Start loader for table data fetching
			if (showLoader) Loader.Start();

			const Report = ReportObj || GetReportById(ReportId);
			if (!Report) {
				TL.Notify && TL.Notify.Banner && TL.Notify.Banner("Not Found", "Report not found");
				return;
			}

			// Track current view and update URL
			State.CurrentReportId = ReportId;
			Navigation.UpdateURLWithReport(ReportId);

			// Toggle UI into single-report mode
			HideElement(Elements.ReportsGrid);
			HideElement(Elements.EmptyState);
			if (Elements.AppWrapper) Elements.AppWrapper.classList.add("single-report-mode");
			// Build report view template
			const ViewTemplate = TL.Template("Report-View");
			if (!ViewTemplate) return;

			// Show a small loading placeholder for the table; real table will be
			// populated by FetchReportTableData which calls BuildDummyReportTable.
			const CatLower = (Report.Category || "").toLowerCase();
			const Filled = TL.FillTemplate(ViewTemplate, {
				Title: Report.DisplayName || Report.Name,
				Description: Report.Description || "Report details",
				InternalName: Report.Name,
				Category: CatLower,
				Icon: Report.Icon || "📊",
				FavoriteClass: State.FavoriteReports.includes(Report.ID) ? "favorited" : "",
				FavoriteTitle: State.FavoriteReports.includes(Report.ID) ? "Remove from favorites" : "Add to favorites",
				Table: '<div class="report-table-loading">Loading table…</div>',
			});

			// Remove any existing view
			const Existing = Elements.MainContent.querySelector(".report-view");
			if (Existing) Existing.remove();

			// Insert at top of main content after header
			const Header = Elements.MainContent.querySelector(".category-header");
			const WrapperDiv = document.createElement("div");
			WrapperDiv.innerHTML = Filled;
			const ViewEl = WrapperDiv.firstElementChild;
			if (Header && ViewEl) {
				Header.after(ViewEl);
			} else if (Elements.MainContent && ViewEl) {
				Elements.MainContent.appendChild(ViewEl);
			}

			// Wire back & generate inside view
			const BackBtn = ViewEl.querySelector(".back-btn");
			if (BackBtn) {
				BackBtn.addEventListener("click", () => {
					HideReportView();
				});
			}

			// Header favorite toggle
			const HeaderFav = ViewEl.querySelector(".favorite-btn");
			if (HeaderFav) {
				HeaderFav.addEventListener("click", () => {
					ToggleFavorite(HeaderFav, Report);
					// Sync footer favorite if list returns later
				});
			}
			// Sticky header compression on scroll
			const HeaderEl = ViewEl.querySelector(".report-view-header");
			if (HeaderEl) {
				const OnScroll = () => {
					const scrolled = window.scrollY || document.documentElement.scrollTop;
					if (scrolled > 40) {
						HeaderEl.classList.add("header-condensed");
					} else {
						HeaderEl.classList.remove("header-condensed");
					}
				};
				window.addEventListener("scroll", OnScroll, { passive: true });
				// Remove listener when view removed
				HeaderEl.addEventListener("cleanup", () => window.removeEventListener("scroll", OnScroll));
			}
			const InnerGenerate = ViewEl.querySelector(".generate-btn");
			if (InnerGenerate) InnerGenerate.addEventListener("click", () => GenerateFilteredReport(Report.Name, Report));

			// Fetch table data asynchronously
			FetchReportTableData(Report)
				.then(() => {
					// Build and render via unified pipeline (filters → build → replace)
					RebuildTableFromState(ViewEl);
					// Initialize search filtering for the rendered table
					InitializeReportTableSearch(ViewEl);
					// Initialize advanced filter (date range) for the rendered table
					InitializeReportFilters(ViewEl);
					// Stop loader after table is rendered
					if (showLoader) Loader.Stop();
				})
				.catch((err) => {
					TL.DebugLog("FetchReportTableData failed:", err);
					// Stop loader on error
					if (showLoader) Loader.Stop();
				});
		}

		/*
		 ** Hide Single Report View & Return To List
		 */
		function HideReportView() {
			State.CurrentReportId = null;
			// Update URL back to category
			Navigation.UpdateURL(State.CurrentCategory || Navigation.GetDefaultCategory());
			const Existing = Elements.MainContent.querySelector(".report-view");
			if (Existing) {
				const HeaderEl = Existing.querySelector(".report-view-header");
				if (HeaderEl) HeaderEl.dispatchEvent(new Event("cleanup"));
				Existing.remove();
			}
			if (Elements.AppWrapper) Elements.AppWrapper.classList.remove("single-report-mode");
			LoadCategory(State.CurrentCategory);
		}

		/*
		 **
		 ** State Management Helper Functions
		 ** ================================
		 */

		/*
		 ** Reset Filters State
		 ** ==================
		 ** Clears all filter states when new data is loaded
		 */
		function ResetFiltersState() {
			State.CurrentFilters = {
				search: "",
				columnFilters: {},
				advancedFilters: [],
			};
			// Also reset pagination state when filters are reset
			ResetPaginationState();
		}

		/*
		 ** Apply Data Filters
		 ** =================
		 ** Filters the current report data based on state filters and returns filtered array
		 */
		function ApplyDataFilters() {
			if (!State.CurrentReportTableData || !Array.isArray(State.CurrentReportTableData)) {
				return [];
			}

			const { search, columnFilters, advancedFilters } = State.CurrentFilters;
			const headers = State.CurrentReportMetadata?.headers || [];

			return State.CurrentReportTableData.filter((row) => {
				// Apply search filter (searches across all columns)
				if (search) {
					const searchLower = search.toLowerCase();
					const rowText = headers
						.map((header) => String(row[header] || ""))
						.join(" ")
						.toLowerCase();
					if (!rowText.includes(searchLower)) {
						return false;
					}
				}

				// Apply column filters
				for (const [columnKey, filterValue] of Object.entries(columnFilters)) {
					if (!filterValue) continue;

					const cellValue = String(row[columnKey] || "");
					const cellValueLower = cellValue.toLowerCase();
					const filterLower = String(filterValue).toLowerCase();

					// Handle different filter types based on value structure
					if (typeof filterValue === "object" && filterValue.from !== undefined && filterValue.to !== undefined) {
						// Date range filter
						const cellDate = new Date(cellValue);
						if (!isNaN(cellDate.getTime())) {
							const cellDateOnly = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());

							if (filterValue.from) {
								const fromDate = new Date(filterValue.from);
								if (!isNaN(fromDate.getTime())) {
									const fromDateOnly = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
									if (cellDateOnly.getTime() < fromDateOnly.getTime()) return false;
								}
							}

							if (filterValue.to) {
								const toDate = new Date(filterValue.to);
								if (!isNaN(toDate.getTime())) {
									const toDateOnly = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate());
									if (cellDateOnly.getTime() > toDateOnly.getTime()) return false;
								}
							}
						}
					} else if (typeof filterValue === "object" && filterValue.min !== undefined && filterValue.max !== undefined) {
						// Number range filter
						const numValue = parseFloat(cellValue);
						if (!isNaN(numValue)) {
							if (filterValue.min !== "" && numValue < parseFloat(filterValue.min)) return false;
							if (filterValue.max !== "" && numValue > parseFloat(filterValue.max)) return false;
						}
					} else {
						// Text/select filter - exact match for selects, contains for text
						if (cellValueLower !== filterLower && !cellValueLower.includes(filterLower)) {
							return false;
						}
					}
				}

				// Apply advanced filters
				for (const rule of advancedFilters) {
					const cellValue = String(row[headers[rule.index]] || "").trim();
					if (!EvaluateAdvancedRule(rule, cellValue)) {
						return false;
					}
				}

				return true;
			});
		}

		/*
		 ** Evaluate Advanced Rule
		 ** =====================
		 ** Evaluates a single advanced filter rule against a cell value
		 */
		function EvaluateAdvancedRule(rule, cellText) {
			const text = (cellText || "").trim();

			if (rule.type === "date") {
				// Parse the cell date
				const cellDate = new Date(text);
				if (isNaN(cellDate.getTime())) return false;

				// Normalize to start of day for date-only comparisons
				const cellDateOnly = new Date(cellDate.getFullYear(), cellDate.getMonth(), cellDate.getDate());

				switch (rule.op) {
					case "date_equals": {
						const compareDate = new Date(rule.valA);
						if (isNaN(compareDate.getTime())) return false;
						const compareDateOnly = new Date(compareDate.getFullYear(), compareDate.getMonth(), compareDate.getDate());
						return cellDateOnly.getTime() === compareDateOnly.getTime();
					}
					case "date_not_equals": {
						const compareDate = new Date(rule.valA);
						if (isNaN(compareDate.getTime())) return false;
						const compareDateOnly = new Date(compareDate.getFullYear(), compareDate.getMonth(), compareDate.getDate());
						return cellDateOnly.getTime() !== compareDateOnly.getTime();
					}
					case "date_before": {
						const compareDate = new Date(rule.valA);
						if (isNaN(compareDate.getTime())) return false;
						const compareDateOnly = new Date(compareDate.getFullYear(), compareDate.getMonth(), compareDate.getDate());
						return cellDateOnly.getTime() < compareDateOnly.getTime();
					}
					case "date_after": {
						const compareDate = new Date(rule.valA);
						if (isNaN(compareDate.getTime())) return false;
						const compareDateOnly = new Date(compareDate.getFullYear(), compareDate.getMonth(), compareDate.getDate());
						return cellDateOnly.getTime() > compareDateOnly.getTime();
					}
					case "date_between": {
						const fromDate = new Date(rule.valA);
						const toDate = new Date(rule.valB);
						if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) return false;
						const fromDateOnly = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
						const toDateOnly = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate());
						return cellDateOnly.getTime() >= fromDateOnly.getTime() && cellDateOnly.getTime() <= toDateOnly.getTime();
					}
					case "date_within_days": {
						const days = parseInt(rule.valA, 10);
						if (isNaN(days) || days <= 0) return false;
						const now = new Date();
						const cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - days);
						return cellDateOnly.getTime() >= cutoffDate.getTime();
					}
					default:
						return true;
				}
			}

			if (rule.type === "number") {
				const n = parseFloat(text);
				if (!Number.isFinite(n)) return false;
				const a = parseFloat(rule.valA);
				const b = parseFloat(rule.valB);

				switch (rule.op) {
					case "eq":
						return n === a;
					case "ne":
						return n !== a;
					case "gt":
						return n > a;
					case "gte":
						return n >= a;
					case "lt":
						return n < a;
					case "lte":
						return n <= a;
					case "between":
						return (Number.isFinite(a) ? n >= a : true) && (Number.isFinite(b) ? n <= b : true);
					default:
						return true;
				}
			}

			// Text operations
			const lower = text.toLowerCase();
			const aText = String(rule.valA || "").toLowerCase();

			switch (rule.op) {
				case "contains":
					return lower.includes(aText);
				case "equals":
					return lower === aText;
				case "not_contains":
					return !lower.includes(aText);
				case "not_equals":
					return lower !== aText;
				default:
					return true;
			}
		}

		/*
		 ** Build Table From Filtered Data
		 ** ==============================
		 ** Generates table HTML from filtered data array with pagination support
		 */
		function BuildTableFromFilteredData(FilteredData) {
			if (!FilteredData || !Array.isArray(FilteredData) || FilteredData.length === 0) {
				// Reset pagination state when no data
				ResetPaginationState();
				return '<div class="report-table-empty">No data matches the current filters.</div>';
			}

			// Use the existing ExtractTableData utility
			const { Headers, Rows } = ExtractTableData(FilteredData);

			if (Headers.length === 0) {
				ResetPaginationState();
				return '<div class="report-table-empty">No valid data structure found.</div>';
			}

			// Store converted row data in pagination state for consistent processing
			State.TablePagination.FilteredData = Rows;
			State.TablePagination.RenderedRowCount = 0;

			// Build the table using the same template pattern
			const TableTemplate = TL.Template("Report-Table");
			if (!TableTemplate) return "";

			// Parse the template into DOM so we can safely manipulate the thead/tbody
			const TempDiv = document.createElement("div");
			TempDiv.innerHTML = TableTemplate;
			const TableEl = TempDiv.querySelector("table");
			if (!TableEl) {
				TL.DebugLog("Report-Table template does not contain a <table> element");
				return TempDiv.innerHTML;
			}

			// Find header row container and tbody
			const TheadTr = TableEl.querySelector("thead tr");
			const Tbody = TableEl.querySelector("tbody");

			if (!TheadTr || !Tbody) {
				TL.DebugLog("Report-Table template is missing <thead><tr> or <tbody>");
				return TempDiv.innerHTML;
			}

			// Clear any placeholder content
			TheadTr.innerHTML = "";
			Tbody.innerHTML = "";

			// Populate headers
			Headers.forEach((HeaderText) => {
				const HeaderCell = document.createElement("th");
				HeaderCell.className = "report-header-cell";
				HeaderCell.textContent = HeaderText;
				TheadTr.appendChild(HeaderCell);
			});

			// Render initial batch of rows using pagination
			const InitialBatch = GetNextRowBatch(Rows);
			RenderRowBatch(InitialBatch, Tbody);

			return TempDiv.innerHTML;
		}

		/*
		 ** Extract Table Data From Array of Objects
		 ** =======================================
		 ** Utility function to extract headers and rows from an array of objects
		 */
		function ExtractTableData(DataArray) {
			if (!Array.isArray(DataArray) || DataArray.length === 0) {
				return { Headers: [], Rows: [] };
			}

			// Extract headers from the first object's keys
			const Headers = Object.keys(DataArray[0]);

			// Extract rows by mapping over each object and getting its values in header order
			const Rows = DataArray.map((RowData) => {
				return Headers.map((HeaderKey) => {
					const CellValue = RowData[HeaderKey];
					// Handle different data types appropriately
					if (CellValue === null || CellValue === undefined) {
						return "";
					}
					if (typeof CellValue === "number") {
						return CellValue.toString();
					}
					if (typeof CellValue === "boolean") {
						return CellValue ? "Yes" : "No";
					}
					return String(CellValue);
				});
			});

			return { Headers, Rows };
		}

		/*
		 ** Reset Pagination State
		 ** =====================
		 ** Clears pagination state when new data is loaded or filters change
		 */
		function ResetPaginationState() {
			State.TablePagination.FilteredData = [];
			State.TablePagination.RenderedRowCount = 0;
			State.TablePagination.IsLoading = false;
		}

		/*
		 ** Get Next Row Batch
		 ** =================
		 ** Returns the next batch of rows to render based on current pagination state
		 */
		function GetNextRowBatch(AllRows) {
			const StartIndex = State.TablePagination.RenderedRowCount;
			const EndIndex = StartIndex + State.TablePagination.BatchSize;
			const NextBatch = AllRows.slice(StartIndex, EndIndex);

			// Update rendered count
			State.TablePagination.RenderedRowCount += NextBatch.length;

			return NextBatch;
		}

		/*
		 ** Render Row Batch
		 ** ===============
		 ** Renders a batch of rows into the provided tbody element
		 */
		function RenderRowBatch(RowBatch, TbodyElement) {
			RowBatch.forEach((RowArray) => {
				const TableRow = document.createElement("tr");
				TableRow.className = "report-row";
				RowArray.forEach((CellValue) => {
					const TableCell = document.createElement("td");
					TableCell.className = "report-cell";
					TableCell.textContent = CellValue;
					TableRow.appendChild(TableCell);
				});
				TbodyElement.appendChild(TableRow);
			});
		}

		/*
		 ** Initialize Scroll Pagination
		 ** ============================
		 ** Sets up scroll-based pagination for the table container
		 */
		function InitializeScrollPagination(TableContainer) {
			if (!TableContainer) {
				return;
			}

			// Reset scroll listener state when initializing new table
			State.TablePagination.ScrollListenerInitialized = false;

			// Find the scrollable container (report-table-scroller)
			const ScrollableContainer = TableContainer.querySelector(".report-table-scroller") || TableContainer;

			if (!ScrollableContainer) {
				TL.DebugLog("No scrollable container found for pagination");
				return;
			}

			// Remove any existing scroll listeners to prevent duplicates
			ScrollableContainer.removeEventListener("scroll", HandleTableScroll);

			// Attach scroll event listener
			ScrollableContainer.addEventListener("scroll", HandleTableScroll);
			State.TablePagination.ScrollListenerInitialized = true;
		}

		/*
		 ** Handle Table Scroll
		 ** ==================
		 ** Handles scroll events to trigger loading more rows
		 */
		function HandleTableScroll(ScrollEvent) {
			// Prevent multiple simultaneous loads
			if (State.TablePagination.IsLoading) {
				return;
			}

			const ScrollContainer = ScrollEvent.target;
			const ScrollTop = ScrollContainer.scrollTop;
			const ScrollHeight = ScrollContainer.scrollHeight;
			const ClientHeight = ScrollContainer.clientHeight;

			// Check if user scrolled near the bottom (50px buffer)
			const ScrollBuffer = 50;
			const DistanceFromBottom = ScrollHeight - (ScrollTop + ClientHeight);
			const IsNearBottom = DistanceFromBottom <= ScrollBuffer;

			if (IsNearBottom) {
				LoadMoreTableRows(ScrollContainer);
			}
		}

		/*
		 ** Load More Table Rows
		 ** ===================
		 ** Loads and renders the next batch of table rows
		 */
		function LoadMoreTableRows(ScrollContainer) {
			// Check if we have more data to load
			const FilteredRowData = State.TablePagination.FilteredData;
			const RenderedCount = State.TablePagination.RenderedRowCount;

			TL.DebugLog(`LoadMoreTableRows called: FilteredRowData.length=${FilteredRowData?.length || 0}, RenderedCount=${RenderedCount}`);

			if (!FilteredRowData || RenderedCount >= FilteredRowData.length) {
				TL.DebugLog("No more rows to load - all data already rendered");
				return;
			}

			// Set loading state
			State.TablePagination.IsLoading = true;

			// Get next batch of rows (FilteredData is already converted to row arrays)
			const NextBatch = GetNextRowBatch(FilteredRowData);

			if (NextBatch.length === 0) {
				State.TablePagination.IsLoading = false;
				return;
			}

			// Find the table body to append rows to
			const TableBody = ScrollContainer.querySelector("table tbody");

			if (!TableBody) {
				TL.DebugLog("Could not find table body for pagination");
				State.TablePagination.IsLoading = false;
				return;
			}

			// Render the new batch of rows
			RenderRowBatch(NextBatch, TableBody);

			// Reset loading state
			State.TablePagination.IsLoading = false;

			TL.DebugLog(`Loaded ${NextBatch.length} more rows. Total rendered: ${State.TablePagination.RenderedRowCount} of ${FilteredRowData.length}`);
		}

		/*
		 **
		 ** Fetch Report Table Data
		 ** =====================
		 ** Fetches real report data using the ["app", "retrieve"] agent,
		 ** stores it in state, and builds the table from the stored data
		 */
		function FetchReportTableData(Report) {
			return new Promise((resolve) => {
				try {
					TL.DebugLog("FetchReportTableData requested for report:", Report && (Report.Name || Report.ID));

					// Call the agent to retrieve actual report data
					TL.Agent({
						agent: ["app", "retrieve"],
						data: {
							ReportName: Report.Name,
						},
						success(ApiData) {
							TL.DebugLog("Report data retrieved from agent:", ApiData);

							// Check if we got valid data
							if (Array.isArray(ApiData) && ApiData.length > 0) {
								// Store the raw API data in state for filtering
								State.CurrentReportTableData = ApiData;

								State.CurrentReportMetadata = {
									report: Report,
									headers: Object.keys(ApiData[0]),
									totalRows: ApiData.length,
								};

								// Reset filters when new data is loaded
								ResetFiltersState();
							} else {
								// If no data or invalid format, set empty dataset and reset state
								State.CurrentReportTableData = [];
								State.CurrentReportMetadata = { report: Report, headers: [], totalRows: 0 };
								ResetFiltersState();
								TL.DebugLog("No valid data returned for report:", Report.Name);
							}

							resolve(true);
						},
						error(error) {
							TL.DebugLog("Agent retrieve failed for report:", Report.Name, error);
							// On error, set empty dataset and reset state
							State.CurrentReportTableData = [];
							State.CurrentReportMetadata = { report: Report, headers: [], totalRows: 0 };
							ResetFiltersState();
							resolve(false);
						},
					});
				} catch (err) {
					TL.DebugLog("FetchReportTableData error:", err);
					// On error, set empty dataset and reset state
					State.CurrentReportTableData = [];
					State.CurrentReportMetadata = { report: Report, headers: [], totalRows: 0 };
					ResetFiltersState();
					resolve(false);
				}
			});
		}

		/*
		 **
		 ** Render Report Table
		 ** ====================
		 ** Injects the generated table HTML into the single-report view.
		 */

		/*
		 ** Initialize Report Table Search
		 ** ==============================
		 ** Wires the toolbar search input to trigger state-based filtering.
		 */
		function InitializeReportTableSearch(ViewEl) {
			if (!ViewEl) return;
			const Wrapper = ViewEl.querySelector(".report-table-wrapper");
			if (!Wrapper) return;
			const SearchInput = Wrapper.querySelector(".rt-search-input");
			if (!SearchInput) return;

			// Debounce input to reduce work while typing
			let DebounceTimer = null;
			const OnInput = (e) => {
				const val = e.target.value;
				if (DebounceTimer) window.clearTimeout(DebounceTimer);
				// Use state-based filtering instead of DOM manipulation
				DebounceTimer = window.setTimeout(() => {
					// Update global search text in state
					State.CurrentFilters.search = val;

					// Rebuild the table via single source-of-truth pipeline
					if (State.CurrentReportTableData) {
						RebuildTableFromState(ViewEl);
					}
				}, 120);
			};

			SearchInput.addEventListener("input", OnInput);
		}

		/**
		 * UpdateFilterToggleIndicator
		 * ---------------------------
		 * Updates the visual indicator on the Filters toggle button to show when any filters are active.
		 * This function can be called from anywhere to update the indicator state.
		 *
		 * @param {HTMLElement} ViewElement - The root element of the single report view (optional, will find current view if not provided)
		 * @returns {void}
		 */
		function UpdateFilterToggleIndicator(ViewElement) {
			// Find the view element if not provided
			const CurrentViewElement = ViewElement || document.querySelector(".report-view");
			if (!CurrentViewElement) return;

			const TableWrapperElement = CurrentViewElement.querySelector(".report-table-wrapper");
			if (!TableWrapperElement) return;

			const FilterToggleButton = TableWrapperElement.querySelector(".rt-filter-btn");
			if (!FilterToggleButton) return;

			// Use state as single source of truth
			const CurrentFilters = State.CurrentFilters || {};

			const HasSearchFilter = Boolean(CurrentFilters.search && CurrentFilters.search.trim());
			const HasColumnFilters = Boolean(CurrentFilters.columnFilters && Object.keys(CurrentFilters.columnFilters).length);
			const HasAdvancedFilters = Boolean(Array.isArray(CurrentFilters.advancedFilters) && CurrentFilters.advancedFilters.length);

			const HasActiveFilters = HasSearchFilter || HasColumnFilters || HasAdvancedFilters;

			FilterToggleButton.classList.toggle("has-active-filters", HasActiveFilters);
			FilterToggleButton.dataset.hasFilters = String(HasActiveFilters);
		}

		/**
		 * UpdateFilterState - Updates the filter state based on user input and updates the indicator state.
		 * @param {Object} Options - Filter update options
		 * @param {string} Options.Type - Type of filter: 'search', 'column', 'advanced'
		 * @param {string} [Options.Key] - For column filters: column key
		 * @param {any} [Options.Value] - The filter value to set (null/undefined to remove)
		 * @param {HTMLElement} [Options.ViewElement] - The view element for indicator updates
		 */
		function UpdateFilterState({ Type, Key, Value, ViewElement }) {
			if (!State.CurrentFilters) {
				State.CurrentFilters = { search: "", columnFilters: {}, advancedFilters: [] };
			}

			switch (Type) {
				case "search":
					State.CurrentFilters.search = Value || "";
					break;

				case "column":
					if (Value === null || Value === undefined || Value === "" || (typeof Value === "object" && Object.values(Value).every((v) => !v))) {
						delete State.CurrentFilters.columnFilters[Key];
					} else {
						State.CurrentFilters.columnFilters[Key] = Value;
					}
					break;

				case "advanced":
					State.CurrentFilters.advancedFilters = Array.isArray(Value) ? Value : [];
					break;
			}

			// Update the Filter indicator
			UpdateFilterToggleIndicator(ViewElement);
		}

		/**
		 * InitializeReportFilters
		 * --------------------------------
		 * Wires up the Report View table filtering UI. This includes:
		 * - Opening/closing the filter panel
		 * - Building per-column dynamic filters (text contains, select options, numeric ranges)
		 * - Providing an advanced rule builder (field + operator + value)
		 * - Applying all filters (including toolbar search) to show/hide table rows
		 *
		 * Notes:
		 * - This function performs DOM queries scoped to the provided view element to avoid leaking across views.
		 * - All internal identifiers use PascalCase for improved readability and consistency within this function.
		 *
		 * @param {HTMLElement} ViewElement - The root element of the single report view where the table and filter UI are rendered.
		 * @returns {void}
		 */
		function InitializeReportFilters(ViewElement) {
			if (!ViewElement) return;
			// Container that wraps the toolbar, table and filter UI for the report view
			const TableWrapperElement = ViewElement.querySelector(".report-table-wrapper");
			if (!TableWrapperElement) return;
			// UI controls
			const FilterToggleButton = TableWrapperElement.querySelector(".rt-filter-btn");
			const FilterPanelElement = TableWrapperElement.querySelector("#reportFilterPanel");
			const ResetFiltersButton = TableWrapperElement.querySelector(".rt-filter-reset");
			const CloseFiltersButton = TableWrapperElement.querySelector(".rt-filter-close");
			if (!FilterToggleButton || !FilterPanelElement) return;

			// Toggle the visibility of the filter panel
			FilterToggleButton.addEventListener("click", () => {
				const IsExpanded = FilterToggleButton.getAttribute("aria-expanded") === "true";
				FilterToggleButton.setAttribute("aria-expanded", String(!IsExpanded));
				FilterPanelElement.hidden = IsExpanded;
			});

			// Close the filter panel when the close button is clicked
			CloseFiltersButton?.addEventListener("click", () => {
				FilterPanelElement.hidden = true;
				FilterToggleButton.setAttribute("aria-expanded", "false");
				// Return focus to the toggle for accessibility
				FilterToggleButton.focus();
			});

			// Also reflect filter indicator on search input changes
			const SearchInputElement = TableWrapperElement.querySelector(".rt-search-input");
			SearchInputElement?.addEventListener("input", () => {
				UpdateFilterState({
					Type: "search",
					Value: SearchInputElement.value,
					ViewElement: ViewElement,
				});
			});

			// Initialize indicator state
			UpdateFilterToggleIndicator(ViewElement);

			// Resolve table elements used for filtering
			const TableElement = TableWrapperElement.querySelector("table");
			const TableBodyElement = TableElement?.tBodies?.[0];
			if (!TableElement || !TableBodyElement) return;

			// Gather table headers for filter processing
			const TableHeaders = Array.from(TableElement.tHead?.rows?.[0]?.cells || []).map((HeaderCell) => HeaderCell.textContent.trim().toLowerCase());

			const DynamicFiltersContainer = TableWrapperElement.querySelector("#rtFilterDynamic");

			// Function to capitalize the first letter of a string
			function Capitalize(Text = "") {
				try {
					return Text.charAt(0).toUpperCase() + Text.slice(1);
				} catch (e) {
					return Text;
				}
			}
			function BuildDynamicFilters() {
				if (!DynamicFiltersContainer) return;
				// Preserve current values for dynamic inputs so they persist across rebuilds
				const PreviousValues = {};
				DynamicFiltersContainer.querySelectorAll("[data-colkey]").forEach((Element) => {
					PreviousValues[Element.dataset.colkey] = Element.value;
				});
				DynamicFiltersContainer.innerHTML = "";
				const Descriptors = [];
				TableHeaders.forEach((HeaderKey, ColumnIndex) => {
					if (!HeaderKey) return;
					const Rows = Array.from(TableBodyElement.rows);
					const Values = [];
					const SampleCount = Math.min(Rows.length, 500);
					for (let r = 0; r < SampleCount; r++) {
						const Text = (Rows[r].cells[ColumnIndex]?.textContent || "").trim();
						if (Text) Values.push(Text);
					}
					const UniqueValues = Array.from(new Set(Values));
					const NumericValues = Values.filter((V) => !isNaN(parseFloat(V)) && isFinite(parseFloat(V)));
					const MajorityNumeric = Values.length > 0 && NumericValues.length / Values.length >= 0.9;

					// Check if this is a date column using the same logic from ColumnsMetadata
					const DatePatterns = [
						/^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
						/^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
						/^\d{1,2}\/\d{1,2}\/\d{4}$/, // M/D/YYYY
						/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/, // ISO datetime
						/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/, // SQL datetime
						/^\w{3} \w{3} \d{1,2} \d{4}/, // Mon Jan 01 2024
						/^\w{3}, \d{1,2} \w{3} \d{4}/, // Mon, 01 Jan 2024
					];
					let DateCount = 0;
					for (const value of Values.slice(0, Math.min(50, Values.length))) {
						const IsDateLike = DatePatterns.some((pattern) => pattern.test(value)) || !isNaN(Date.parse(value));
						if (IsDateLike && value.length >= 8) {
							const ParsedDate = new Date(value);
							if (ParsedDate instanceof Date && !isNaN(ParsedDate.getTime())) {
								DateCount++;
							}
						}
					}
					const MajorityDate = Values.length > 0 && DateCount / Math.min(50, Values.length) >= 0.7;

					if (MajorityDate) {
						// Create date range filter
						const RowEl = document.createElement("div");
						RowEl.className = "rt-filter-row";
						const LabelEl = document.createElement("label");
						LabelEl.textContent = Capitalize(HeaderKey);
						const FromInput = document.createElement("input");
						FromInput.type = "date";
						FromInput.className = "rt-input";
						FromInput.placeholder = "from";
						FromInput.dataset.colkey = HeaderKey + "__from";
						FromInput.dataset.colindex = ColumnIndex;
						const SeparatorEl = document.createElement("span");
						SeparatorEl.className = "rt-sep";
						SeparatorEl.textContent = "to";
						const ToInput = document.createElement("input");
						ToInput.type = "date";
						ToInput.className = "rt-input";
						ToInput.placeholder = "to";
						ToInput.dataset.colkey = HeaderKey + "__to";
						ToInput.dataset.colindex = ColumnIndex;
						if (PreviousValues[FromInput.dataset.colkey]) FromInput.value = PreviousValues[FromInput.dataset.colkey];
						if (PreviousValues[ToInput.dataset.colkey]) ToInput.value = PreviousValues[ToInput.dataset.colkey];

						// Add event listeners for state-based filtering
						const UpdateDateRangeFilter = () => {
							const Headers = State.CurrentReportMetadata?.headers || [];
							const ColumnKey = Headers[ColumnIndex];
							if (ColumnKey) {
								UpdateFilterState({
									Type: "column",
									Key: ColumnKey,
									Value: {
										from: FromInput.value,
										to: ToInput.value,
									},
								});
								RebuildTableFromState(ViewElement);
							}
						};
						FromInput.addEventListener("input", UpdateDateRangeFilter);
						ToInput.addEventListener("input", UpdateDateRangeFilter);

						RowEl.append(LabelEl, FromInput, SeparatorEl, ToInput);
						DynamicFiltersContainer.appendChild(RowEl);
						Descriptors.push({ type: "date-range", index: ColumnIndex, get: () => ({ from: FromInput.value, to: ToInput.value }) });
					} else if (MajorityNumeric) {
						const RowEl = document.createElement("div");
						RowEl.className = "rt-filter-row";
						const LabelEl = document.createElement("label");
						LabelEl.textContent = Capitalize(HeaderKey);
						const MinInput = document.createElement("input");
						MinInput.type = "number";
						MinInput.className = "rt-input";
						MinInput.placeholder = "min";
						MinInput.dataset.colkey = HeaderKey + "__min";
						MinInput.dataset.colindex = ColumnIndex;
						const SeparatorEl = document.createElement("span");
						SeparatorEl.className = "rt-sep";
						SeparatorEl.textContent = "-";
						const MaxInput = document.createElement("input");
						MaxInput.type = "number";
						MaxInput.className = "rt-input";
						MaxInput.placeholder = "max";
						MaxInput.dataset.colkey = HeaderKey + "__max";
						MaxInput.dataset.colindex = ColumnIndex;
						if (PreviousValues[MinInput.dataset.colkey]) MinInput.value = PreviousValues[MinInput.dataset.colkey];
						if (PreviousValues[MaxInput.dataset.colkey]) MaxInput.value = PreviousValues[MaxInput.dataset.colkey];

						// Add event listeners for state-based filtering
						const UpdateNumberRangeFilter = () => {
							const Headers = State.CurrentReportMetadata?.headers || [];
							const ColumnKey = Headers[ColumnIndex];
							if (ColumnKey) {
								UpdateFilterState({
									Type: "column",
									Key: ColumnKey,
									Value: {
										min: parseFloat(MinInput.value) || undefined,
										max: parseFloat(MaxInput.value) || undefined,
									},
								});
								RebuildTableFromState(ViewElement);
							}
						};
						MinInput.addEventListener("input", UpdateNumberRangeFilter);
						MaxInput.addEventListener("input", UpdateNumberRangeFilter);

						RowEl.append(LabelEl, MinInput, SeparatorEl, MaxInput);
						DynamicFiltersContainer.appendChild(RowEl);
						Descriptors.push({ type: "number-range", index: ColumnIndex, get: () => ({ min: parseFloat(MinInput.value), max: parseFloat(MaxInput.value) }) });
					} else if (UniqueValues.length > 0 && UniqueValues.length <= 12) {
						UniqueValues.sort();
						const RowEl = document.createElement("div");
						RowEl.className = "rt-filter-row";
						const LabelEl = document.createElement("label");
						LabelEl.textContent = Capitalize(HeaderKey);
						const SelectEl = document.createElement("select");
						SelectEl.className = "rt-select-input";
						SelectEl.dataset.colkey = HeaderKey;
						SelectEl.dataset.colindex = ColumnIndex;
						const AnyOption = document.createElement("option");
						AnyOption.value = "";
						AnyOption.textContent = "Any";
						SelectEl.appendChild(AnyOption);
						UniqueValues.forEach((V) => {
							const OptionEl = document.createElement("option");
							OptionEl.value = V;
							OptionEl.textContent = V;
							SelectEl.appendChild(OptionEl);
						});
						if (PreviousValues[HeaderKey]) SelectEl.value = PreviousValues[HeaderKey];

						// Add event listener for state-based filtering
						const UpdateSelectFilter = () => {
							const Headers = State.CurrentReportMetadata?.headers || [];
							const ColumnKey = Headers[ColumnIndex];
							if (ColumnKey) {
								UpdateFilterState({
									Type: "column",
									Key: ColumnKey,
									Value: SelectEl.value?.trim(),
								});
								RebuildTableFromState(ViewElement);
							}
						};
						SelectEl.addEventListener("change", UpdateSelectFilter);

						RowEl.append(LabelEl, SelectEl);
						DynamicFiltersContainer.appendChild(RowEl);
						Descriptors.push({ type: "select", index: ColumnIndex, get: () => SelectEl.value });
					} else {
						const RowEl = document.createElement("div");
						RowEl.className = "rt-filter-row";
						const LabelEl = document.createElement("label");
						LabelEl.textContent = Capitalize(HeaderKey);
						const InputEl = document.createElement("input");
						InputEl.type = "text";
						InputEl.className = "rt-input";
						InputEl.placeholder = "contains…";
						InputEl.dataset.colkey = HeaderKey;
						InputEl.dataset.colindex = ColumnIndex;
						if (PreviousValues[HeaderKey]) InputEl.value = PreviousValues[HeaderKey];

						// Add event listener for state-based filtering
						const UpdateTextFilter = () => {
							const Headers = State.CurrentReportMetadata?.headers || [];
							const ColumnKey = Headers[ColumnIndex];
							if (ColumnKey) {
								UpdateFilterState({
									Type: "column",
									Key: ColumnKey,
									Value: InputEl.value?.trim(),
								});
								RebuildTableFromState(ViewElement);
							}
						};
						InputEl.addEventListener("input", UpdateTextFilter);

						RowEl.append(LabelEl, InputEl);
						DynamicFiltersContainer.appendChild(RowEl);
						Descriptors.push({ type: "text", index: ColumnIndex, get: () => InputEl.value });
					}
				});
				FilterPanelElement._descriptors = Descriptors;
			}

			// --- Dynamic Filter Builder (Field + Operator + Value) ---

			const FieldSelectElement = TableWrapperElement.querySelector("#rtFieldSelect");
			const OperatorSelectElement = TableWrapperElement.querySelector("#rtOperatorSelect");
			const ValueInputWrapper = TableWrapperElement.querySelector("#rtValueInputWrap");
			const AddFilterButton = TableWrapperElement.querySelector(".rt-add-filter");
			const ActiveFiltersContainer = TableWrapperElement.querySelector("#rtActiveFilters");

			FilterPanelElement._activeFilters = FilterPanelElement._activeFilters || [];

			// Determine column types by sampling values and collect unique values for select operators
			const ColumnsMetadata = TableHeaders.map((HeaderKey, ColumnIndex) => {
				const Rows = Array.from(TableBodyElement.rows);
				const SampleCount = Math.min(Rows.length, 500);
				let NumericCount = 0,
					DateCount = 0,
					NonEmptyCount = 0;

				// Track unique values for potential select operators
				const UniqueValuesSet = new Set();
				const MaxUniqueForSelect = 50; // Limit unique values to prevent huge dropdowns

				// Date detection patterns
				const DatePatterns = [
					/^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
					/^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
					/^\d{1,2}\/\d{1,2}\/\d{4}$/, // M/D/YYYY
					/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/, // ISO datetime
					/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/, // SQL datetime
					/^\w{3} \w{3} \d{1,2} \d{4}/, // Mon Jan 01 2024
					/^\w{3}, \d{1,2} \w{3} \d{4}/, // Mon, 01 Jan 2024
				];

				for (let r = 0; r < SampleCount; r++) {
					const Text = (Rows[r].cells[ColumnIndex]?.textContent || "").trim();
					if (!Text) continue;
					NonEmptyCount++;

					// Check if value is numeric
					if (!isNaN(parseFloat(Text)) && isFinite(parseFloat(Text))) NumericCount++;

					// Check if value matches date patterns
					const IsDateLike = DatePatterns.some((pattern) => pattern.test(Text)) || !isNaN(Date.parse(Text));
					if (IsDateLike && Text.length >= 8) {
						// Minimum reasonable date length
						const ParsedDate = new Date(Text);
						if (ParsedDate instanceof Date && !isNaN(ParsedDate.getTime())) {
							DateCount++;
						}
					}

					// Collect unique values (up to limit) for select operator consideration
					if (UniqueValuesSet.size < MaxUniqueForSelect) {
						UniqueValuesSet.add(Text);
					}
				}

				// Determine field type with priority: date > number > text
				let InferredType = "text";
				if (NonEmptyCount > 0) {
					if (DateCount / NonEmptyCount >= 0.7) {
						InferredType = "date";
					} else if (NumericCount / NonEmptyCount >= 0.9) {
						InferredType = "number";
					}
				}

				// Convert Set to sorted array for consistent ordering
				const UniqueValues = Array.from(UniqueValuesSet).sort();

				// Determine if this field should offer select operators
				// Fields with 2-20 unique values are good candidates for select operators
				const SupportsSelectOperators = UniqueValues.length >= 2 && UniqueValues.length <= State.UniqueValuesThreshold;

				return {
					index: ColumnIndex,
					key: HeaderKey,
					type: InferredType,
					uniqueValues: UniqueValues,
					supportsSelectOperators: SupportsSelectOperators,
				};
			});

			const OperatorsMap = {
				text: [
					{ id: "contains", label: "contains" },
					{ id: "equals", label: "equals" },
					{ id: "not_contains", label: "does not contain" },
					{ id: "not_equals", label: "does not equal" },
				],
				number: [
					{ id: "eq", label: "equals (=)" },
					{ id: "ne", label: "not equal (≠)" },
					{ id: "gt", label: "greater than (>)" },
					{ id: "gte", label: "greater than or equal (≥)" },
					{ id: "lt", label: "less than (<)" },
					{ id: "lte", label: "less than or equal (≤)" },
					{ id: "between", label: "between" },
				],
				date: [
					{ id: "date_equals", label: "is on" },
					{ id: "date_before", label: "is before" },
					{ id: "date_after", label: "is after" },
					{ id: "date_between", label: "is between" },
					{ id: "date_not_equals", label: "is not on" },
					{ id: "date_within_days", label: "is within last X days" },
				],
				// NEW: Dedicated operators for fields that support select dropdowns
				// These will be used when a field has a small number of unique values
				select: [
					{ id: "contains", label: "contains" }, // Text input for partial matching
					{ id: "equals", label: "equals" }, // Single select for exact match
					{ id: "not_equals", label: "does not equal" }, // Single select for exclusion
					{ id: "not_contains", label: "does not contain" }, // Text input for partial exclusion
				],
			};

			// Display-only labels for filter chips (words only, no symbols)
			const OperatorDisplayLabels = {
				eq: "equals",
				ne: "not equal",
				gt: "greater than",
				gte: "greater than or equal",
				lt: "less than",
				lte: "less than or equal",
				between: "between",
				date_between: "between",
				contains: "contains",
				equals: "equals",
				not_contains: "does not contain",
				not_equals: "does not equal",
				date_equals: "is on",
				date_before: "is before",
				date_after: "is after",
				date_not_equals: "is not on",
				date_within_days: "is within last X days",
			};

			function FillFieldOptions() {
				if (!FieldSelectElement) return;
				FieldSelectElement.innerHTML = "";
				const AnyOption = document.createElement("option");
				AnyOption.value = "";
				AnyOption.textContent = "Select field";
				FieldSelectElement.appendChild(AnyOption);
				ColumnsMetadata.forEach((ColumnMeta) => {
					const OptionEl = document.createElement("option");
					OptionEl.value = String(ColumnMeta.index);
					OptionEl.textContent = Capitalize(ColumnMeta.key);
					OptionEl.dataset.type = ColumnMeta.type;
					// Store additional metadata for select operator support
					OptionEl.dataset.supportsSelect = ColumnMeta.supportsSelectOperators;
					OptionEl.dataset.uniqueValues = JSON.stringify(ColumnMeta.uniqueValues);
					FieldSelectElement.appendChild(OptionEl);
				});
			}

			function FillOperatorOptions(Type, SupportsSelectOperators = false) {
				if (!OperatorSelectElement) return;
				OperatorSelectElement.innerHTML = "";

				// Choose operator set based on field characteristics
				// If field supports select operators (few unique values), use select operators
				// Otherwise, use standard text/number operators
				let OperatorsList;
				if (SupportsSelectOperators && Type === "text") {
					OperatorsList = OperatorsMap.select;
				} else {
					OperatorsList = OperatorsMap[Type || "text"] || OperatorsMap.text;
				}

				const AnyOption = document.createElement("option");
				AnyOption.value = "";
				AnyOption.textContent = "Select operator";
				OperatorSelectElement.appendChild(AnyOption);
				OperatorsList.forEach((Operator) => {
					const OptionEl = document.createElement("option");
					OptionEl.value = Operator.id;
					OptionEl.textContent = Operator.label;
					OperatorSelectElement.appendChild(OptionEl);
				});
			}

			function RenderValueInput(Type, Operator, UniqueValues = []) {
				if (!ValueInputWrapper) return;
				ValueInputWrapper.innerHTML = "";

				const MakeInput = (InputType, Id, customPlaceholder = null) => {
					const InputEl = document.createElement("input");
					InputEl.type = InputType;
					InputEl.className = "rt-input";
					if (customPlaceholder) {
						InputEl.placeholder = customPlaceholder;
					} else if (InputType === "date") {
						InputEl.placeholder = "yyyy-mm-dd";
					} else if (InputType === "number") {
						InputEl.placeholder = "number";
					} else {
						InputEl.placeholder = "value";
					}
					if (Id) InputEl.id = Id;
					return InputEl;
				};

				// Handle date-specific operators
				if (Type === "date") {
					if (Operator === "date_between") {
						// Two date inputs for range
						const FromInput = MakeInput("date", "rtValA");
						const SeparatorEl = document.createElement("span");
						SeparatorEl.className = "rt-sep";
						SeparatorEl.textContent = "to";
						const ToInput = MakeInput("date", "rtValB");
						ValueInputWrapper.append(FromInput, SeparatorEl, ToInput);
						ValueInputWrapper._getValue = () => ({ a: FromInput.value, b: ToInput.value });
						return;
					} else if (Operator === "date_within_days") {
						// Number input for days
						const DaysInput = MakeInput("number", "rtValA", "number of days");
						DaysInput.min = "1";
						DaysInput.max = "365";
						ValueInputWrapper.append(DaysInput);
						ValueInputWrapper._getValue = () => ({ a: DaysInput.value });
						return;
					} else if (Operator && Operator.startsWith("date_")) {
						// Single date input for other date operators
						const DateInput = MakeInput("date", "rtValA");
						ValueInputWrapper.append(DateInput);
						ValueInputWrapper._getValue = () => ({ a: DateInput.value });
						return;
					}
				}

				// Handle select for equality when unique values are small (≤ 20)
				if ((Operator === "equals" || Operator === "not_equals") && UniqueValues.length > 0 && UniqueValues.length <= State.UniqueValuesThreshold) {
					// Create single-select dropdown for "equals" / "does not equal" operators
					const SelectEl = document.createElement("select");
					SelectEl.className = "rt-input";
					SelectEl.id = "rtValA";

					// Add empty option for single-select
					const EmptyOption = document.createElement("option");
					EmptyOption.value = "";
					EmptyOption.textContent = "Select value...";
					SelectEl.appendChild(EmptyOption);

					// Add options from unique values
					UniqueValues.forEach((value) => {
						const OptionEl = document.createElement("option");
						OptionEl.value = value;
						OptionEl.textContent = value;
						SelectEl.appendChild(OptionEl);
					});

					ValueInputWrapper.appendChild(SelectEl);

					// Single-select: Returns single selected value
					ValueInputWrapper._getValue = () => ({
						a: SelectEl.value,
					});
				} else if (Operator === "between") {
					// Handle "between" operator with two inputs
					const ValueAInput = MakeInput(Type === "number" ? "number" : "text", "rtValA");
					const SeparatorEl = document.createElement("span");
					SeparatorEl.className = "rt-sep";
					SeparatorEl.textContent = "–";
					const ValueBInput = MakeInput(Type === "number" ? "number" : "text", "rtValB");
					ValueInputWrapper.append(ValueAInput, SeparatorEl, ValueBInput);
					ValueInputWrapper._getValue = () => ({ a: ValueAInput.value, b: ValueBInput.value });
				} else {
					// Handle all other operators with single text/number input
					const ValueAInput = MakeInput(Type === "number" ? "number" : "text", "rtValA");
					ValueInputWrapper.append(ValueAInput);
					ValueInputWrapper._getValue = () => ({ a: ValueAInput.value });
				}
			}

			function OnFieldChange() {
				const SelectedOption = FieldSelectElement?.selectedOptions?.[0];
				const Type = SelectedOption?.dataset?.type || "text";

				// Extract field metadata for select operator support
				const SupportsSelect = SelectedOption?.dataset?.supportsSelect === "true";
				const UniqueValues = SelectedOption?.dataset?.uniqueValues ? JSON.parse(SelectedOption.dataset.uniqueValues) : [];

				// Fill operators based on field characteristics
				FillOperatorOptions(Type, SupportsSelect);

				// Auto-select "equals" operator when a field is selected (only if a field is actually selected)
				if (SelectedOption && SelectedOption.value && OperatorSelectElement) {
					// Find the "equals" option in the operator dropdown
					const EqualsOption = Array.from(OperatorSelectElement.options).find((option) => option.value === "equals");
					if (EqualsOption) {
						OperatorSelectElement.value = "equals";
						// Trigger the operator change to update value inputs
						OnOperatorChange();
					}
				} else {
					// Render value input based on field type and select operator support (for when no field is selected)
					RenderValueInput(Type, "", UniqueValues);
				}
			}

			function OnOperatorChange() {
				const SelectedOption = FieldSelectElement?.selectedOptions?.[0];
				const Type = SelectedOption?.dataset?.type || "text";

				// Extract unique values for select operators
				const UniqueValues = SelectedOption?.dataset?.uniqueValues ? JSON.parse(SelectedOption.dataset.uniqueValues) : [];

				// Render value input based on selected operator
				RenderValueInput(Type, OperatorSelectElement?.value || "", UniqueValues);
			}

			function RenderActiveFilters() {
				if (!ActiveFiltersContainer) return;
				ActiveFiltersContainer.innerHTML = "";
				FilterPanelElement._activeFilters.forEach((Filter, Index) => {
					const ChipEl = document.createElement("div");
					ChipEl.className = "rt-chip";
					const LabelEl = document.createElement("span");
					LabelEl.className = "rt-chip-label";

					// Format value text based on operator type
					let ValueText;
					if (Filter.op === "between" || Filter.op === "date_between") {
						ValueText = `${Filter.valA} – ${Filter.valB}`;
					} else {
						// For all other operators, show the value directly
						ValueText = Filter.valA;
					}

					// Use display-only labels for filter chips (words only, no symbols)
					const displayLabel = OperatorDisplayLabels[Filter.op] || Filter.label;
					LabelEl.textContent = `${Capitalize(Filter.key)} ${displayLabel} ${ValueText}`;
					const RemoveButton = document.createElement("button");
					RemoveButton.type = "button";
					RemoveButton.className = "rt-chip-remove";
					RemoveButton.textContent = "×";
					RemoveButton.setAttribute("aria-label", `Remove filter ${Filter.key}`);
					RemoveButton.addEventListener("click", () => {
						FilterPanelElement._activeFilters.splice(Index, 1);
						UpdateFilterState({
							Type: "advanced",
							Value: FilterPanelElement._activeFilters,
							ViewElement: ViewElement,
						});
						RenderActiveFilters();
						ApplyFilters();
					});
					ChipEl.append(LabelEl, RemoveButton);
					ActiveFiltersContainer.appendChild(ChipEl);
				});
			}

			function AddCurrentFilter() {
				const FieldOptionEl = FieldSelectElement?.selectedOptions?.[0];
				const OperatorId = OperatorSelectElement?.value || "";
				if (!FieldOptionEl || !FieldOptionEl.value || !OperatorId || !ValueInputWrapper || !ValueInputWrapper._getValue) return;
				const ColumnIndex = parseInt(FieldOptionEl.value, 10);
				const Meta = ColumnsMetadata.find((C) => C.index === ColumnIndex) || { type: "text" };
				const Value = ValueInputWrapper._getValue();
				if (!Value || (!Value.a && OperatorId !== "between")) return;
				const Rule = {
					index: ColumnIndex,
					key: FieldOptionEl.textContent || Meta.key,
					type: Meta.type,
					op: OperatorId,
					valA: (Value.a || "").trim(),
					valB: (Value.b || "").trim(),
					label: (OperatorsMap[Meta.type] || OperatorsMap.text).find((O) => O.id === OperatorId)?.label || OperatorId,
				};
				FilterPanelElement._activeFilters.push(Rule);
				UpdateFilterState({
					Type: "advanced",
					Value: FilterPanelElement._activeFilters,
					ViewElement: ViewElement,
				});
				RenderActiveFilters();
				ApplyFilters();

				// Clear the filter form fields after successfully adding the filter
				ClearFilterForm();
			}

			function ClearFilterForm() {
				// Reset field selection to default
				if (FieldSelectElement) {
					FieldSelectElement.selectedIndex = 0;
				}

				// Reset operator selection to default
				if (OperatorSelectElement) {
					OperatorSelectElement.selectedIndex = 0;
				}

				// Clear value inputs (both text inputs and select elements)
				if (ValueInputWrapper) {
					// Clear text inputs
					const ValueInputs = ValueInputWrapper.querySelectorAll("input");
					ValueInputs.forEach((input) => {
						input.value = "";
					});

					// Clear select elements (single-select dropdowns for select operators)
					const ValueSelects = ValueInputWrapper.querySelectorAll("select");
					ValueSelects.forEach((select) => {
						// Reset single-select to first option (usually empty option)
						select.selectedIndex = 0;
					});
				}

				// Trigger the field change event to reset the operator options and value inputs
				OnFieldChange();
			}

			FieldSelectElement?.addEventListener("change", OnFieldChange);
			OperatorSelectElement?.addEventListener("change", OnOperatorChange);
			AddFilterButton?.addEventListener("click", AddCurrentFilter);

			FillFieldOptions();
			OnFieldChange();

			function ApplyFilters() {
				if (!State.CurrentReportTableData) {
					TL.DebugLog("No report data available for filtering");
					return;
				}
				// Rebuild table via centralized pipeline (filters → build → replace)
				RebuildTableFromState(ViewElement);
			}

			// Build dynamic filters initially
			BuildDynamicFilters();

			ResetFiltersButton?.addEventListener("click", () => {
				// Clear search input
				const SearchInput = TableWrapperElement.querySelector(".rt-search-input");
				if (SearchInput) SearchInput.value = "";

				// Clear advanced filter builder filters
				FilterPanelElement._activeFilters = [];
				RenderActiveFilters();

				// Clear dynamic column filters
				if (DynamicFiltersContainer) {
					DynamicFiltersContainer.querySelectorAll("input, select").forEach((El) => (El.value = ""));
				}

				// Reset state filters
				ResetFiltersState();

				// Update indicator immediately on reset
				UpdateFilterToggleIndicator(ViewElement);

				// Apply the reset state
				ApplyFilters();
			});

			// Rebuild and apply when table re-renders
			TableElement.addEventListener("reporttable:loaded", () => {
				BuildDynamicFilters();

				ApplyFilters();
			});
		}

		/*
		 ** Replace Report Table Content (Global)
		 ** ===================================
		 ** Replaces the table HTML within the current report view and re-initializes
		 ** scroll pagination. Emits a 'reporttable:loaded' event after replacement.
		 */
		function ReplaceReportTableContent(ViewEl, NewTableHTML) {
			if (!ViewEl) return;
			try {
				const Selectors = [".report-table-container", ".report-table", ".report-table-wrapper", ".report-view-table", ".report-view-table-container", ".report-table-area"];
				let Container = null;
				for (let i = 0; i < Selectors.length; i++) {
					Container = ViewEl.querySelector(Selectors[i]);
					if (Container) break;
				}
				if (!Container) Container = ViewEl.querySelector(".report-view") || ViewEl;

				Container.innerHTML = NewTableHTML || '<div class="report-table-empty">No data matches the current filters.</div>';

				// Re-init scroll pagination after DOM update
				InitializeScrollPagination(Container);

				const TableEl = Container.querySelector("table");
				if (TableEl && typeof CustomEvent === "function") {
					TableEl.dispatchEvent(new CustomEvent("reporttable:loaded", { detail: { view: ViewEl, filtered: true } }));
				}
			} catch (err) {
				TL.DebugLog("ReplaceReportTableContent error:", err);
			}
		}

		/*
		 ** Rebuild Table From State (Global)
		 ** ================================
		 ** Single source-of-truth pipeline:
		 ** 1) ApplyDataFilters()
		 ** 2) BuildTableFromFilteredData() with pagination
		 ** 3) ReplaceReportTableContent() and rewire scroll
		 */
		function RebuildTableFromState(ViewEl) {
			if (!State.CurrentReportTableData) {
				TL.DebugLog("RebuildTableFromState: No data available");
				return;
			}
			const FilteredData = ApplyDataFilters();
			const NewTableHTML = BuildTableFromFilteredData(FilteredData);
			ReplaceReportTableContent(ViewEl, NewTableHTML);
			TL.DebugLog(`Rebuilt table: ${FilteredData.length} of ${State.CurrentReportTableData.length} rows shown`);
		}

		/*
		 **
		 ** Show Empty State
		 ** ===============
		 */
		function ShowEmptyState() {
			if (Elements.ReportsGrid) HideElement(Elements.ReportsGrid);
			if (Elements.EmptyState) ShowElement(Elements.EmptyState);
		}

		/*
		 **
		 ** Generate Report (for Report Cards)
		 ** ==================================
		 ** Downloads full report data from server (no filters applied)
		 */
		function GenerateReport(ReportName, ReportInfo) {
			TL.DebugLog("Generate report requested:", ReportName, ReportInfo);

			// Check if user is already downloading a report
			const MainWrapper = document.querySelector("main") || document.body;
			if (MainWrapper.querySelector && MainWrapper.querySelector(".TL-Loading")) {
				TL.Notify.Banner("Please wait", "Please wait for the current report to finish downloading");
				return false;
			}

			// Start the download process
			DownloadReport(ReportName, ReportInfo);
		}

		/*
		 **
		 ** Open Popup For Report (Report Cards only)
		 ** ========================================
		 ** If a report defines a Popup type (e.g., RANGE or DATE), show a popup
		 ** to collect date inputs and then export using the returned data.
		 */
		function OpenReportPopup(ReportInfo) {
			const PopupType = String(ReportInfo?.Popup || "").toUpperCase();
			if (!PopupType) {
				GenerateReport(ReportInfo.Name, ReportInfo);
				return;
			}

			const Inputs = {};
			if (PopupType === "RANGE") {
				Inputs.From = { type: "date", require: true, label: "From" };
				Inputs.To = { type: "date", require: true, label: "To" };
			} else {
				Inputs.From = { type: "date", require: true, label: "Date" };
			}
			Inputs.Report = { type: "hidden", require: true, value: ReportInfo.Name };

			TL.Inputs.PopupForm({
				Heading: PopupType === "RANGE" ? "Select Dates" : "Select Date",
				Inputs,
				SubmitText: "Submit",
				OnSubmit: function (FormValues, Callback) {
					TL.Agent({
						agent: ["app", "get-report"], // NOTE: Might need to change the agent this calls
						data: FormValues,
						async success(Result) {
							try {
								await ExportReport(Result, ReportInfo);
								Callback(true);
							} catch (err) {
								TL.Notify.Banner("Error", err || "Failed to export report");
								Callback(false);
							}
						},
						error(error) {
							TL.Notify.Banner("Oops!", error);
							Callback(false);
						},
					});
				},
			});
		}

		/*
		 **
		 ** Generate Filtered Report (for Report View)
		 ** ==========================================
		 ** Downloads currently filtered/visible data from the report view
		 */
		function GenerateFilteredReport(ReportName, ReportInfo) {
			TL.DebugLog("Generate filtered report requested:", ReportName, ReportInfo);

			// Check if user is already downloading a report
			const MainWrapper = document.querySelector("main") || document.body;
			if (MainWrapper.querySelector && MainWrapper.querySelector(".TL-Loading")) {
				TL.Notify.Banner("Please wait", "Please wait for the current report to finish downloading");
				return false;
			}

			// Get the currently filtered data
			const FilteredData = ApplyDataFilters();

			if (!FilteredData || FilteredData.length === 0) {
				TL.Notify.Banner("No Data", "No data available to download with current filters");
				return false;
			}

			// Start the download process with filtered data
			DownloadFilteredData(FilteredData, ReportInfo);
		}

		/*
		 **
		 ** Toggle Favorite Status
		 ** =====================
		 */
		function ToggleFavorite(FavoriteBtn, Report) {
			const ReportId = Report.ID;
			const IsFavorited = State.FavoriteReports.includes(ReportId);

			if (IsFavorited) {
				// Removing from favorites: call server-side RemoveFavorite via agent
				try {
					if (FavoriteBtn) FavoriteBtn.disabled = true;
					Loader.Start(FavoriteBtn);
					RemoveFavorite(ReportId)
						.then((Result) => {
							// Remove from local state on success
							State.FavoriteReports = State.FavoriteReports.filter((Id) => Id !== ReportId);
							if (FavoriteBtn) {
								FavoriteBtn.classList.remove("favorited");
								FavoriteBtn.title = "Add to favorites";
							}
							TL.DebugLog("Removed from favorites:", Report.Name, Result);
							// Update UI
							UpdateFavoritesData();
							UpdateFavoritesCount();
							if (State.CurrentCategory === "favorites") {
								LoadCategory("favorites");
							}
						})
						.catch((err) => {
							TL.Notify.Banner("Error", err || "Failed to remove favorite");
							TL.DebugLog("RemoveFavorite failed:", err);
						})
						.finally(() => {
							if (FavoriteBtn) FavoriteBtn.disabled = false;
							Loader.Stop(FavoriteBtn);
						});
				} catch (err) {
					TL.DebugLog("ToggleFavorite remove error:", err);
					Loader.Stop(FavoriteBtn);
				}

				return;
			}

			// Adding to favorites: call server-side AddFavorite via agent
			try {
				if (FavoriteBtn) FavoriteBtn.disabled = true;
				Loader.Start(FavoriteBtn);
				AddFavorite(ReportId)
					.then((Result) => {
						// Only add if not already present (avoid duplicates)
						if (!State.FavoriteReports.includes(ReportId)) {
							// Put newly added favorites at the front so they appear first in the UI
							State.FavoriteReports.unshift(ReportId);
						}
						FavoriteBtn.classList.add("favorited");
						FavoriteBtn.title = "Remove from favorites";
						TL.DebugLog("Added to favorites:", Report.Name, Result);
						// Update UI
						UpdateFavoritesData();
						UpdateFavoritesCount();
						if (State.CurrentCategory === "favorites") {
							LoadCategory("favorites");
						}
					})
					.catch((err) => {
						TL.DebugLog("AddFavorite failed:", err);
						TL.Notify.Banner("Error", err || "Failed to add favorite");
					})
					.finally(() => {
						if (FavoriteBtn) FavoriteBtn.disabled = false;
						Loader.Stop(FavoriteBtn);
					});
			} catch (err) {
				TL.DebugLog("ToggleFavorite error:", err);
				Loader.Stop(FavoriteBtn);
			}
		}

		/*
		 **
		 ** Update Favorites Data
		 ** ====================
		 */
		function UpdateFavoritesData() {
			const FavoriteReports = [];

			// Build a map of reports by ID for quick lookup across all categories
			const ReportById = {};
			Object.keys(State.ReportData).forEach((CatKey) => {
				const Cat = State.ReportData[CatKey];
				if (!Cat || !Array.isArray(Cat.Reports)) return;
				Cat.Reports.forEach((R) => {
					if (R && R.ID !== undefined) ReportById[R.ID] = { ...R, OriginalCategory: CatKey };
				});
			});

			// Map favorite IDs to report objects, preserving order and deduping
			const Seen = new Set();
			State.FavoriteReports.forEach((FavId) => {
				const Rep = ReportById[FavId];
				if (Rep && !Seen.has(FavId)) {
					// Push to preserve the server-provided order (ServerFavorites are ordered by created_at DESC)
					FavoriteReports.push(Rep);
					Seen.add(FavId);
				}
			});

			// Update The Favorites Category Data
			State.ReportData.favorites.Reports = FavoriteReports;
		}

		/*
		 **
		 ** Update Favorites Count Display
		 ** =============================
		 */
		function UpdateFavoritesCount() {
			if (Elements.FavoritesCount) {
				const Count = State.FavoriteReports.length;
				Elements.FavoritesCount.textContent = `${Count} Report${Count === 1 ? "" : "s"}`;
			}
		}

		/*
		 **
		 ** Get Reports
		 ** =====================
		 */
		function GetReports() {
			return new Promise((resolve, reject) => {
				TL.Agent({
					agent: ["app", "get-user-reports"],
					data: {},
					success(Result) {
						TL.DebugLog("Fetched Reports:", Result);

						resolve(Result);
					},
					error(error) {
						TL.Notify.Banner("Error", error);
						reject(error);
					},
				});
			});
		}

		/*
		 **
		 ** Get Report Categories
		 ** ====================
		 */
		function GetReportCategories() {
			return new Promise((resolve, reject) => {
				TL.Agent({
					agent: ["app", "get-report-categories"],
					data: {},
					success(Result) {
						resolve(Result || {});
					},
					error(error) {
						TL.DebugLog("GetReportCategories error:", error);
						reject(error);
					},
				});
			});
		}

		/*
		 **
		 ** Export Report Using Downloader
		 ** ==============================
		 */
		async function ExportReport(Data, ReportInfo) {
			try {
				// Use the Downloader to export the report with the correct file type and display name
				return await TL.Downloader.Export({
					Data,
					FileType: ReportInfo.Format || "csv",
					DisplayName: ReportInfo.DisplayName || ReportInfo.Name,
					ConfigurationName: ReportInfo.Name,
				});
			} catch (error) {
				TL.DebugLog("Export error:", error);
				TL.Notify.Banner("Export Error", "Failed to export the report");
			}
		}

		/*
		 **
		 ** Download Filtered Data
		 ** =====================
		 ** Downloads pre-filtered data without server round-trip
		 */
		async function DownloadFilteredData(FilteredData, ReportInfo) {
			// Start loading
			Loader.Start();

			try {
				TL.DebugLog("Downloading filtered data:", FilteredData.length, "rows");

				// Export the filtered data using the same export function
				await ExportReport(FilteredData, ReportInfo);
				TL.DebugLog("Filtered report exported successfully");
			} catch (error) {
				TL.Notify.Banner("Error", error || "Failed to export filtered data");
				TL.DebugLog("DownloadFilteredData error:", error);
			} finally {
				Loader.Stop();
			}
		}

		/*
		 **
		 ** Retrieve and Download Report
		 ** ============================
		 */
		async function DownloadReport(ReportName, ReportInfo) {
			// Start loading
			Loader.Start();

			try {
				// Get the report data using the same agent as FetchReportTableData
				const Result = await new Promise((resolve, reject) => {
					TL.Agent({
						agent: ["app", "retrieve"],
						data: {
							ReportName: ReportName,
						},
						success(Result) {
							TL.DebugLog("Report retrieved for download:", Result);
							resolve(Result);
						},
						error(error) {
							TL.DebugLog("Download report retrieve failed:", error);
							reject(error);
						},
					});
				});

				TL.DebugLog("Report data retrieved for download:", Result);

				// Check if we have valid data to export
				if (Array.isArray(Result) && Result.length > 0) {
					// Export the real report data using the downloader
					await ExportReport(Result, ReportInfo);
					TL.DebugLog("Report exported successfully:", ReportName);
				} else {
					// Handle case where no data was returned
					TL.Notify.Banner("No Data", "No data available for this report");
					TL.DebugLog("No data to export for report:", ReportName);
				}
			} catch (error) {
				TL.Notify.Banner("Error", error || "Failed to retrieve report data");
				TL.DebugLog("DownloadReport error:", error);
			} finally {
				Loader.Stop();
			}
		}
	});

	/*
	 **
	 ** Get Favorites
	 ** ============
	 */
	function GetFavorites() {
		return new Promise((resolve, reject) => {
			TL.Agent({
				agent: ["app", "get-favorites"],
				data: {},
				success(Result) {
					TL.DebugLog("Favorites retrieved:", Result);
					resolve(Result);
				},
				error(error) {
					reject(error);
				},
			});
		});
	}

	/*
	 **
	 ** Add Favorite
	 ** ============
	 */
	function AddFavorite(ReportId) {
		return new Promise((resolve, reject) => {
			TL.Agent({
				agent: ["app", "add-favorite"],
				data: {
					ReportId: ReportId,
				},
				success(Result) {
					TL.DebugLog("Favorite added:", Result);
					resolve(Result);
				},
				error(error) {
					reject(error);
				},
			});
		});
	}

	/*
	 **
	 ** Remove Favorite
	 ** ===============
	 */
	function RemoveFavorite(ReportId) {
		return new Promise((resolve, reject) => {
			TL.Agent({
				agent: ["app", "remove-favorite"],
				data: {
					ReportId: ReportId,
				},
				success(Result) {
					TL.DebugLog("Favorite removed:", Result);
					resolve(Result);
				},
				error(error) {
					reject(error);
				},
			});
		});
	}
});
