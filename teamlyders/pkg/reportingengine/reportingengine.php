<?php

namespace ReportingEngine;

// Script for the class traits
require_once 'utils/utils.php';


class ReportingEngine
{
    // Inject the trait into the class
    use Utils;

    protected $TL;
    protected $ReportEngineDB;
    public $Error;

    // Default directory for scripts
    const POST_SCRIPTS_DIR = __DIR__ . '/scripts';

    //Resolve the dynamic placeholder {{}} in our queries
    const DYNAMIC_PLACEHOLDER_REGEX = '/{{\s*(.+?)\s*}}/'; //trim surrounding spaces


    /*
    **
    **
    **
    **
    **
    **
    ** Constructor
    ** ======================================
    */
    public function __construct($TL)
    {

        // Establish a global instance of TL
        $this->TL = $TL;

        // Create a connection to the Reporting DB
        $this->ReportEngineDB = $this->GetDatabaseConnection('Reporting');
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the Reporting DB
    ** ==================================
    */
    private function GetReport(string $ReportName): ?array
    {
        // Get the requested report
        $Query = "SELECT * FROM `Reports` WHERE `Name` = :Name";
        if (!$Result = $this->ReportEngineDB->Query($Query, [':Name' => $ReportName])) {
            $this->Error = "Unable to find Report $ReportName";
            $this->TL->Log($this->Error, 'ERROR');
            return null;
        }

        // Get the report and decode the report definition
        $Report = $Result[0];
        $DecodedReport = json_decode($Report['Report Definition'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->Error = "Unable to decode Report $ReportName";
            $this->TL->Log($this->Error, 'ERROR');
            return null;
        }
        // Create the $Report['Report'] entry
        $Report['Report'] = $DecodedReport;
        return $Report;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    ** ==================================
    */
    public function GetReportById(string $ReportId): ?array
    {
        $Query = "SELECT * FROM `Reports` WHERE `ID` = :ID";
        if (!$Result = $this->ReportEngineDB->Query($Query, [':ID' => $ReportId])) {
            $this->Error = "Unable to find Report with ID: $ReportId";
            $this->TL->Log($this->Error, 'ERROR');
            return null;
        }


        $Report = $Result[0] ?? [];
        $DecodedReport = json_decode($Report['Report Definition'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->Error = "Unable to decode Report with ID: $ReportId";
            $this->TL->Log($this->Error, 'ERROR');
            return null;
        }
        $Report['Report'] = $DecodedReport;
        return $Report;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    **  Pre process / sql / post process
    ** ==================================
    */
    public function Retrieve(array $Params = []): ?array
    {
        /**EXECUTION ORDER DATA PIPELINE
         * 1.  Pre-Process:  used to mutate input Params
         * 2.  Raw SQL  NOTE:  can only execute RAW or PARSED.. NOT BOTH
         * 3.  Parsed SQL
         * 4.  Post Process:  Mutate results
         * 5.  PHP Scripts:  Mutate results
         */

        // Get the report name
        $ReportName = $Params['ReportName'] ?? null;

        // Get the report and extract the details
        if (!$Result = $this->GetReport($ReportName)) {
            return null;
        }

        // Get all the report parts!
        $Report = $Result['Report'] ?? null;
        $PreProcess = $Report['pre_process'] ?? null;
        $RawSQL = $Report['raw_sql'] ?? null;
        $SQLParts = $Report['sql'] ?? null;
        $PostProcess = $Report['post_process'] ?? null;
        $PHPScripts = $Report['php_scripts'] ?? null;
        $AdditionalReports = $Report['additional_reports'] ?? null;


        // Verify Params is an array prior to kick off
        $Params = is_array($Params) ? $Params : [];
        // Data return object
        $MasterResult = [];


        // Params gets passed by REFERENCE.. can be modified!!
        if ($PreProcess) {
            $this->RunProcess($PreProcess, $Params, 'pre_process');
        }

        // Process SQL.. Raw or Parsed.. cannot do both as the data pipeline will not support this
        if ($RawSQL) {
            $MasterResult = $this->ProcessRawSQL($RawSQL, $Params);
        } elseif ($SQLParts) {
            $MasterResult = $this->ProcessParsedSQL($SQLParts, $Params);
        }

        // Normalize to array.. in case something went wrong
        $MasterResult = is_array($MasterResult) ? $MasterResult : [];

        // MasterResult is passed by REFERENCE.. can be modified!!
        if ($PostProcess) {
            $this->RunProcess($PostProcess, $MasterResult, 'post_process');
        }

        // Run any php scripts... MasterResults is passed by REFERENCE.... can be modified
        if ($PHPScripts) {
            $MasterResult = $this->RunPHPScripts($PHPScripts, $Params, $MasterResult);
        }

        // Verify we have  result array
        if (!is_array($MasterResult)) {
            $this->TL->Agent->Error("Something failed... data is in an unusable format");
            return null;
        }

        // See if additional reports need to run and data merged
        if (!$AdditionalReports) {
            // Nope...return the results
            return $MasterResult;
        }

        // We have more reports to run.. they should all execute and be indexed separately in the final data set
        $FinalResults = [];

        // If we have master results...index and merge into final data set
        if (!empty($MasterResult)) {
            $MasterOutputKey = $Report['output']['output_key'] ?? $ReportName;
            $FinalResults[$MasterOutputKey] = $MasterResult;
        }

        // Loop through all reports, execute and index results
        foreach ($AdditionalReports as $Index => $Row) {

            // Child report to run
            $ChildReportName = $Row['report_name'] ?? null;

            // Verify we have a report name to run
            if (!$ChildReportName) {
                continue; // skip if not defined
            }

            // Grab the output key and create an input params set for the child report
            $OutputKey = $Row['output_key'] ?? $ChildReportName;
            $ChildParams = array_merge($Params, ['ReportName' => $ChildReportName]);

            // Retrieve the report results, index and merge into the final data set
            $Result = $this->Retrieve($ChildParams);
            if (!empty($Result)) {
                $FinalResults[$OutputKey] = $Result;
            }
        }
        // return the final data set
        return $FinalResults;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Parse the sql query and execute the request
    ** ==================================
    */

    private function ProcessParsedSQL(array $SQL, array $Params): array
    {
        // Build query parts from report definition json
        $QueryParts = $this->BuildQueryParts($SQL, $Params);
        if (!$QueryParts) {
            $this->TL->Log("Parsed SQL failed to produce query parts", 'ERROR');
            return [];
        }
        // Execute the query parts
        $Result = $this->ExecuteQuery($QueryParts, $SQL);
        if (!$Result) {
            $this->TL->Log("Parsed SQL query failed to produce results", 'ERROR');
            return [];
        }

        // verify we are returning an array
        if (!is_array($Result)) {
            return [];
        }
        return $Result;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    ** ==================================
    */
    private function BuildQueryParts(array $Report, array $Params): array
    {
        $SelectParts = [];
        $JoinParts = [];
        $WhereParts = [];
        $GroupBy = [];
        $OrderBy = [];
        $Bindings = [];
        $SQL = '';

        // Verify we have a source.. not source go straight to post processing functions
        if (empty($Report['source'])) {
            return [];
        }


        // Main table and alias source
        $mainAlias = $Report['source']['alias'] ?? 'a';
        $mainTable = "`{$Report['source']['table']}` AS {$mainAlias}";

        // === SELECT: fields ===
        $RawFields = $Report['fields'] ?? [];
        $Fields = $this->GetFields($RawFields);
        $SelectParts = array_merge($SelectParts, $Fields);

        // === SELECT: computed aggregates ===
        $RawAggregates = $Report['aggregates'] ?? [];
        $Aggregates = $this->GetAggregates($RawAggregates);
        $SelectParts = array_merge($SelectParts, $Aggregates);

        // === SELECT: calculated metrics ===
        $RawMetrics = $Report['calculated_metrics'] ?? [];
        $CalculatedMetrics = $this->GetCalculatedMetrics($RawMetrics);
        $SelectParts = array_merge($SelectParts, $CalculatedMetrics);

        // === JOINs ===
        $RawJoins = $Report['joins'] ?? [];
        $JoinParts = $this->GetJoins($RawJoins);

        // === FILTERS (with {{placeholders}}) ===
        $RawFilters = $Report['filters'] ?? [];
        $Filters = $this->GetFilters($RawFilters, $Params);
        $WhereParts = $Filters['Where Parts'] ?? [];
        $Bindings = $Filters['Bindings'] ?? [];

        // === GROUP BY ===
        $RawGroupBy = $Report['group_by'] ?? [];
        $GroupBy = $this->GetGroupBy($RawGroupBy);

        // === ORDER BY ===
        $RawOrderBy = $Report['order_by'] ?? [];
        $OrderBy = $this->GetOrderBy($RawOrderBy);


        // === Final SQL Assembly ===
        $SQL = "SELECT " . implode(", ", $SelectParts) .
            " FROM {$mainTable} " .
            (!empty($JoinParts) ? implode(" ", $JoinParts) : '') .
            (!empty($WhereParts) ? " WHERE " . implode(" AND ", $WhereParts) : '') .
            (!empty($GroupBy) ? " GROUP BY " . implode(", ", $GroupBy) : '') .
            (!empty($OrderBy) ? " ORDER BY " . implode(", ", $OrderBy) : '');


        // === LIMIT / OFFSET ===
        // Values need to be ints.. cannot bind
        if (isset($Report['limit']) && is_int($Report['limit'])) {
            $Limit = (int)$Report['limit'];
            $SQL = $SQL . " LIMIT $Limit ";
        }
        if (isset($Report['offset']) && is_int($Report['offset'])) {
            $Offset = (int)$Report['offset'];
            $SQL = $SQL . " OFFSET $Offset ";
        }

        // Return the query and bindings
        return [
            'Query' => $SQL,
            'Bindings' => $Bindings
        ];
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    ** ==================================
    */
    private function GetAggregates(array $Aggregates): array
    {
        // Verify it is an array and not empty
        if (!is_array($Aggregates) || empty($Aggregates)) {
            return [];
        }

        // Return values
        $SelectParts = [];

        // Loop through the aggregates adding to SQL
        foreach ($Aggregates as $Aggregate) {

            $Expression = '';
            $Function = '';
            // Get the alias for the aggregate
            $Alias = $Aggregate['alias'] ?? $Aggregate['name'] ?? 'computed';

            // If computed field exists, we have to pull the re-usable function from the DB
            if (isset($Aggregate['computed_field'])) {

                // Lookup in computed_fields table
                $Result = $this->ReportEngineDB->Query("SELECT Expression, `Aggregate Function` FROM `Computed Fields` WHERE Name = :Name", [':Name' => $Aggregate['computed_field']]);

                // Computed field could not be found in the DB... log error and skip aggregate
                if (!$Result) {
                    $this->Error = "Computed Field {$Aggregate['computed_field']} could not be found";
                    $this->TL->Log($this->Error, 'ERROR');
                    continue;
                }

                // Get the computed field result and extract the function (SUM,AVG,MAX...) and expression (total_sales - refunds)
                $ComputedField = $Result[0];
                $RawExpression = $ComputedField['Expression'];
                $Function = $ComputedField['Aggregate Function'] ?? null;

                // Bindings must be provided in the report JSON to map placeholders to real columns
                $Bindings = $Aggregate['bindings'] ?? [];

                // Replace placeholders like {{var}} with actual fields from bindings
                $Expression = preg_replace_callback(self::DYNAMIC_PLACEHOLDER_REGEX, function ($matches) use ($Bindings, $Aggregate) {
                    $Key = $matches[1];
                    if (!isset($Bindings[$Key])) {
                        $this->TL->Log("Missing binding for placeholder {{$Key}} in computed_field '{$Aggregate['computed_field']}'", 'ERROR');
                        return 'NULL';
                    }
                    return $Bindings[$Key];
                }, $RawExpression);


                // Form the SQL for the aggregate
                $SQL = $Function ? "{$Function}({$Expression})" : $Expression;
            } else {
                // Aggregate is not in DB Table...Ad-hoc definition in report json
                $Expression = $Aggregate['expression'] ?? '';
                $Function = $Aggregate['aggregate_function'] ?? null;
                $SQL = $Function ? "{$Function}({$Expression})" : $Expression;
            }


            // Add the SQL to the Select Parts array
            $SelectParts[] = "{$SQL} AS `{$Alias}`";
        }

        return $SelectParts;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    ** ==================================
    */
    private function GetCalculatedMetrics(array $Metrics): array
    {
        // Verify it is an array and not empty
        if (!is_array($Metrics) || empty($Metrics)) {
            return [];
        }

        // Return values
        $SelectParts = [];

        // Loop through the aggregates adding to SQL
        foreach ($Metrics as $Metric) {
            $Expression = '';
            $Function = '';

            // Get the metric name to query from Computed Metrics table
            $Name = $Metric['name'] ?? null;
            // Get the metric name
            if (!$Name) {
                $this->Error = "Metric name is missing in input metric definition.";
                $this->TL->Log($this->Error, 'ERROR');
                continue;
            }

            // Get the alias for the aggregate
            $Alias = $Metric['alias'] ?? $Metric['name'] ?? 'computed';



            // Get the metric data
            if (!$Result = $this->ReportEngineDB->Query("SELECT Expression, `Aggregate Function` FROM `Calculated Metrics` WHERE Name = :Name", [':Name' => $Name])) {
                $this->Error = "Query failed for computed metric {$Name}.";
                $this->TL->Log($this->Error, 'ERROR');
                continue;
            }

            // Computed metric could not be found in the DB... log error and continue to the next metric

            if (!isset($Result[0]) || !isset($Result[0]['Expression'])) {
                $this->Error = "Computed metric '{$Name}' not found or malformed.";
                $this->TL->Log($this->Error, 'ERROR');
                continue;
            }
            // Get the computed field result and extract the function (SUM,AVG,MAX...) and expression (total_sales - refunds)
            $RawExpression = $Result[0]['Expression'];
            $Function = $Result[0]['Aggregate Function'] ?? null;

            $ValidFunctions = ['SUM', 'AVG', 'MIN', 'MAX', 'COUNT', null];
            if (!in_array($Function, $ValidFunctions, true)) {
                $this->TL->Log("Invalid aggregate function for '{$Name}'", 'ERROR');
                continue;
            }


            // Bindings must be provided in the report JSON to map placeholders to real columns
            $Bindings = $Metric['bindings'] ?? [];

            $HasBindingError = false;
            // Replace placeholders like {{var}} with actual fields from bindings
            $Expression = preg_replace_callback(self::DYNAMIC_PLACEHOLDER_REGEX, function ($matches) use ($Bindings, $Name, &$HasBindingError) {

                // Get the key
                $Key = $matches[1];

                if (!isset($Bindings[$Key])) {
                    $this->TL->Log("Missing binding for placeholder $Key in computed metric $Name", 'ERROR');
                    $HasBindingError = true;
                    return 'NULL';
                }
                $BindParts = $Bindings[$Key];

                // Allow bind parts string expressions
                if (is_string($BindParts)) {
                    return $BindParts;
                }

                // Do not have string, verify we have components to stich together the metric source
                if (empty($BindParts['source']) || empty($BindParts['column'])) {
                    $this->TL->Log("Missing bind components for key {$Key} in computed metric $Name", 'ERROR');
                    $HasBindingError = true;
                    return 'NULL';
                }

                return "{$BindParts['source']}.`{$BindParts['column']}`";
            }, $RawExpression);
            if ($HasBindingError) {
                $this->TL->Log("One or more bindings missing or malformed in computed metric '{$Name}'", 'ERROR');
                continue;
            }

            // Form the SQL for the aggregate
            $SQL = $Function ? "{$Function}({$Expression})" : $Expression;

            // Add the SQL to the Select Parts array
            $SelectParts[] = "{$SQL} AS `{$Alias}`";
        }
        return $SelectParts;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Execute raw sql statements
    ** ==================================
    */
    private function ProcessRawSQL($RawSQL, $Params): array
    {
        $ClientErrorMessage = "Something failed... no results found.";
        // Extract raw sql parts and check for required parts
        $Query = $RawSQL['query'] ?? null;
        $Database = $RawSQL['database'] ?? null;
        $RawBindings = $RawSQL['bindings'] ?? [];
        if (!$Query) {
            $this->Error = $ClientErrorMessage;
            $this->TL->Log("Raw SQL is missing query param", 'ERROR');
            return [];
        }

        // Create a connection the to the DB source if needed.... verify
        $Connection = $this->GetDatabaseConnection($Database);
        if (!$Connection) {
            $this->Error = $ClientErrorMessage;
            $this->TL->Log("Failed to create DB connection for Raw SQL.", 'ERROR');
            return [];
        }

        // Create the bindings
        $Bindings = [];
        $ParamCounter = 0;

        // Loop through the bindings to create named parameters
        foreach ($RawBindings as $Key => $Value) {

            // Get the bound VALUE
            $BindValue = $this->ResolvePlaceholder($Value, $Params);

            // If the bound value is array, we need to format it into a query STRING
            if (is_array($BindValue)) {

                $Placeholders = [];
                foreach ($BindValue as $Index => $V) {
                    $BindKey = ":param{$ParamCounter}";
                    $Placeholders[] = $BindKey;
                    $Bindings[$BindKey] = $V;
                    $ParamCounter++;
                }
                // Create the string from array params
                $InClause = "(" . implode(',', $Placeholders) . ")";

                // Replace the bound param with the value
                $Query = preg_replace('/' . preg_quote($Key, '/') . '\b/', $InClause, $Query, 1);
            } else {
                $Bindings[$Key] = $BindValue;
            }
        }

        // Execute the query and return results
        if (!$Result = $Connection->Query($Query, $Bindings)) {
            $this->TL->Log("Raw SQL query '$Query' failed to produce a result.", 'ERROR');
            $this->TL->Log(json_encode($Connection->Error), 'ERROR');
            $this->Error = $ClientErrorMessage;
            return [];
        }
        return $Result;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    // **  Get the select fields
    ** ==================================
    */
    private function GetFields(array $Fields): array
    {
        // Verify it is an array and not empty
        if (!is_array($Fields) || empty($Fields)) {
            return [];
        }

        // Return values
        $SelectParts = [];

        // Loop through the selected fields and add them to the Select Parts
        foreach ($Fields as $Field) {
            $Alias = isset($Field['alias']) ? " AS `{$Field['alias']}`" : '';
            $Source = $Field['source'] ?? '';
            $Column = $Field['column'] ?? '';
            $SelectParts[] = "{$Source}.`{$Column}`{$Alias}";
        }
        return $SelectParts;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Construct the group by clause
    ** ==================================
    */
    private function GetGroupBy(array $Groups): array
    {
        // Verify it is an array and not empty
        if (!is_array($Groups) || empty($Groups)) {
            return [];
        }
        return array_values($Groups);
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Construct the order by clause
    ** ==================================
    */
    private function GetOrderBy(array $Orders): array
    {
        // Verify it is an array and not empty
        if (!is_array($Orders) || empty($Orders)) {
            return [];
        }

        $OrderBy = [];

        foreach ($Orders as $Order) {
            // Grab the order by field
            $Field = $Order['Field'] ?? null;
            if ($Field === null) {
                $this->TL->Log("Field missing in ORDER BY clause", 'ERROR');
                continue;
            }
            // Get the order direction
            $Direction = strtoupper($Order['direction'] ?? 'ASC');
            // Default to ASC
            if (!in_array($Direction, ['ASC', 'DESC'])) {
                $Direction = 'ASC';
            }
            $OrderBy[] = "`{$Field}` " . $Direction;
        }
        return $OrderBy;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Construct the joins
    ** ==================================
    */
    private function GetJoins(array $Joins): array
    {
        // Verify it is an array and not empty
        if (!is_array($Joins) || empty($Joins)) {
            return [];
        }

        // Return values
        $JoinParts = [];

        // Loop through the selected fields and add them to the Select Parts
        foreach ($Joins as $Join) {
            $Table = $Join['source']['table'] ?? '';
            $Alias = $Join['source']['alias'] ?? '';

            // Fully-qualified join clause
            $JoinClause = strtoupper(($Join['type'] ?? '')) . " JOIN `{$Table}` AS {$Alias}";
            $JoinParts[] = "{$JoinClause} ON {$Join['on']}";
        }
        return $JoinParts;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Construct the filters
    ** ==================================
    */
    private function GetFilters(array $Filters, $Params): array
    {


        // Verify it is an array and not empty
        if (!is_array($Filters) || empty($Filters)) {
            return [];
        }

        // Return values
        $WhereParts = [];
        $Bindings = [];
        $ParamCounter = 0;

        // Loop through the Filters, resolve the value, create the WHERE clause and add to the bindings
        foreach ($Filters as $Filter) {
            // Get the field / column name
            $Field = $Filter['field'] ?? "";
            // Transform operator to uppercase
            $Operator = strtoupper(($Filter['operator'] ?? '='));


            // Get the value... could be array, placeholder may need to be resolved {{User ID}}
            $Value = $Filter['value'] ?? '';

            // === Between Operator ===
            // See if we have 2 dates
            if ($Operator === 'BETWEEN' && is_array($Value) && count($Value) === 2) {

                // Resolve the 2 dates
                $Value1 = $this->ResolvePlaceholder($Value[0], $Params);
                $Value2 = $this->ResolvePlaceholder($Value[1], $Params);
                // Create the binding param for the query
                $Param1 = ":param" . $ParamCounter++;
                $Param2 = ":param" . $ParamCounter++;
                // Add the clause to the Where Parts
                $WhereParts[] = "{$Field} BETWEEN {$Param1} AND {$Param2}";
                // Update the Bindings
                $Bindings[$Param1] = $Value1;
                $Bindings[$Param2] = $Value2;
            } elseif ($Operator === 'IN') {
                // === IN Operator ===
                $Resolved = $this->ResolvePlaceholder($Value, $Params);
                // Need to transform array to string and bind
                if (is_array($Resolved)) {
                    if (empty($Resolved)) {
                        // No matches possible; force false predicate
                        $WhereParts[] = "1 = 0";
                        continue;
                    }
                    foreach ($Resolved as $Index => $V) {
                        $Key = ":param{$ParamCounter}";
                        $Placeholders[] = $Key;
                        $Bindings[$Key] = $V;
                        $ParamCounter++;
                    }
                    $InClause = implode(',', $Placeholders);
                    $WhereParts[] = "{$Field} IN ({$InClause})";
                }
            } else {
                // === NOT BETWEEN and NOT IN.. just resolve the placeholder and bind ===
                $Resolved = $this->ResolvePlaceholder($Value, $Params);

                $Param = ":param" . $ParamCounter++;
                $WhereParts[] = "{$Field} {$Operator} {$Param}";
                $Bindings[$Param] = $Resolved;
            }
        }
        // Return the query components
        return ['Where Parts' => $WhereParts, 'Bindings' => $Bindings];
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Resolve the {{}} placeholder in Values from the Params array
    ** ==================================
    */
    private function ResolvePlaceholder($Value, $Params)
    {
        // Match our {{Variable}}
        if (is_string($Value) && preg_match(self::DYNAMIC_PLACEHOLDER_REGEX, $Value, $matches)) {

            // Get the key.. matches [0] is the literal match {{Key}}
            $Key = $matches[1];

            // Try exact match
            if (array_key_exists($Key, $Params)) return $Params[$Key];

            // Check if " " need to be replaced with "_".. this happens with keys with spaces sent in XHR requests
            $AltKey = str_replace(' ', '_', $Key);
            if (array_key_exists($AltKey, $Params)) return $Params[$AltKey];

            // Value not found in params....
            $this->TL->Log("Missing runtime parameter: {$Key}", 'ERROR');
            return null;
        }
        // No matches or not string.. return input
        return $Value;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Create a connection to DatabaseName
    ** ==================================
    */
    public function GetDatabaseConnection($DatabaseName)
    {
        $Connection = null;
        // Determine if we are initiating this request from a local environment
        $IsLocal = ($this->TL->Location['domainExtension'] === "local") ? true : false;
        $Environment = $IsLocal ? 'DEVELOPMENT' : 'PRODUCTION';
        $ConnectionName = "{$Environment}-{$DatabaseName}";

        try {
            // Set the correct database for the correct environment
            $Connection = new \System\Database($this->TL, $ConnectionName);
        } catch (\Throwable $th) {
            $this->Error = "Something failed creating a database connection: $ConnectionName";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }
        // Config all set
        return $Connection;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Execute a query from the components
    ** ==================================
    */
    private function ExecuteQuery($QueryComponents, $SQLParts): array
    {

        // Verify required params
        $RequiredParams = ['Query'];
        if (!$this->ValidateRequiredParams($RequiredParams, $QueryComponents)) {
            return [];
        }

        // Extract the query components
        $Query = $QueryComponents['Query'];
        $Bindings = $QueryComponents['Bindings'] ?? [];

        // Get the source database from the Report
        if (!$DatabaseName = $SQLParts['source']['database'] ?? null) {
            $this->Error = "Could not create a database connection: {$SQLParts['source']['database']}";
            $this->TL->Log($this->Error, 'ERROR');
            return [];
        }

        // Create a connection to the source table for the query
        $Connection = $this->GetDatabaseConnection($DatabaseName);

        // Execute the query
        if (!$Result = $Connection->Query($Query, $Bindings)) {
            $this->Error = "Query failed to produce valid results";
            $this->TL->Log($this->Error, 'ERROR');
            $this->TL->Log("Query Failed: $Query", 'ERROR');
            $this->TL->Log(json_encode($Connection->Error), 'ERROR');
            return [];
        }
        return $Result;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    ** Validate all required params are present 
    ** ==================================
    */
    private function ValidateRequiredParams(array $Required, array $Params): bool
    {
        // Verify params are not empty
        if (empty($Required) ||  empty($Params)) {
            $this->Error = "Missing required Parameters: Input arrays are empty";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }

        // Verify Params has the required keys
        foreach ($Required as $Index => $Require) {
            if (!array_key_exists($Require, $Params)) {
                $this->Error = "Missing required Param {$Require}";
                $this->TL->Log($this->Error, 'ERROR');
                return false;
            }
        }
        return true;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Runs php scripts...NOTE: Scripts have direct access to $PARAMS and $DATA... PASSED BY REFERENCE
    ** ==================================
    */
    private function RunPHPScripts(array $Scripts, $PARAMS = [], &$DATA = [])
    {
        // Get the base directory of the post process scripts and normalize it across different operating systems
        $BaseDir = realpath(self::POST_SCRIPTS_DIR);

        if (!$BaseDir) {
            $this->TL->Log("Invalid post scripts base directory", 'ERROR');
            return $DATA;
        }

        if (!is_array($Scripts)) {
            $this->TL->Log("Error:  post_process scripts are not in the proper format... expected array", 'ERROR');
            return $DATA;
        }

        // Ensure $DATA is an array
        if (!is_array($DATA)) {
            $DATA = [];
        }
        // Loop through all script names
        foreach ($Scripts as $ScriptName) {

            // Build the relative filepath add extension .... some/path/to/TestScript.php
            $RelativeFilePath = $BaseDir . DIRECTORY_SEPARATOR . $ScriptName . DIRECTORY_SEPARATOR . $ScriptName . ".php";

            // Resolve the relative filepath to the absolute file path
            $AbsoluteFilePath = realpath($RelativeFilePath);

            // Validate path..
            // Verify we have a path...Verify the absolute path starts with the base path
            if (!$AbsoluteFilePath || strpos($AbsoluteFilePath, $BaseDir) !== 0) {
                $this->TL->Log("Script path traversal or not found: {$ScriptName}", 'ERROR');
                continue;
            }

            // Require the script... the script may exit or modify the $DATA reference...NOTE:  PASSED BY REFERENCE so scripts have direct access
            try {
                require_once $AbsoluteFilePath;
            } catch (Throwable $e) {
                // Includes both Exception and Error
                $this->TL->Log("Error loading script {$ScriptName}: " . $e->getMessage(), 'ERROR');
                continue;
            }
        }
        // This will return the $DATA object...It will only execute if the scripts do not exit prior... requires any script that executes to modify the
        // Shared $DATA variable to be returned
        return $DATA;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Runs Pre/Post processes... Mutates the $DataRef array....PASSED BY REFERENCE
    ** ==================================
    */
    private function RunProcess(array $Processes,  &$DataRef = [], $Phase = 'pre_process'): void
    {
        // Validate required keys
        if (!is_array($Processes) || empty($Processes)) {
            $this->TL->Log("Error:  Processes for $Phase is not in the correct format", 'ERROR');
            return;
        }

        // Loop through the pre_process array executing the functions
        foreach ($Processes as $Index => $Process) {
            // Get the function name and output key
            $Function = $Process['function'] ?? null;
            $OutputKey = $Process['output_key'] ?? $Function;


            // Verify function argument exists
            if (!$Function) {
                $this->Error = "Missing required function param in {$Phase}";
                $this->TL->Log($this->Error, 'ERROR');
                continue;
            }

            // Verify class method exists on $this
            if (!method_exists($this, $Function)) {
                $this->TL->Log("Method '{$Function}' not found in step {$Phase}", 'ERROR');
                continue;
            }

            // Call the function and update the $DataRef if the function returned value
            $Result = $this->$Function($DataRef);
            if (!is_array($Result)) {
                $this->TL->Log("Function '{$Function}' did not return array in {$Phase}", 'ERROR');
                continue;
            }

            // Update the Data...pre_process is modifying input Params.. post_process is modifying results
            // pre_process
            if ($Phase === 'pre_process') {
                $DataRef[$OutputKey] = $Result;
            } elseif ($Phase === 'post_process') {
                // post_process
                $DataRef = $Result;
            }
        }
        // Return void... input params are passed by ref.. mutated
        return;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get a users reports based on permission
    ** ==================================
    */
    public function GetUserReports(): ?array
    {

        // Set the query
        $Query = "SELECT `ID`, `Name`, `Display Name`, `Description`, `Category`, `Format`, `Icon`, `Version`, `Permissions`, `Popup`, `View Inline` FROM `Reports` WHERE `Is Active` = 1";

        // Execute the query
        if (!$Result = $this->ReportEngineDB->Query($Query)) {
            $this->Error = "No active reports found";
            return null;
        }

        // Main return array
        $Reports = [];

        // Add the reports the user has permissions for to return object
        foreach ($Result as $Index => $Row) {
            if (empty($Row['Permissions'])) continue;

            // Skip reports with null or empty category
            if (empty($Row['Category'])) continue;

            // Add the report if user has permission
            if ($this->VerifyReportPermissions($Row['Permissions'])) {
                // Remove permission.. not send to the client
                unset($Row['Permissions']);

                // Index reports by category;
                $Reports[$Row['Category']] ??= [];
                $Reports[$Row['Category']][] = $Row;
            }
        }

        // Return result
        return $Reports;
    }
    /* 
    **
    **
    **
    **
    **
    ** 
    **  Return bool user based permission
    ** ==================================
    */
    private function VerifyReportPermissions(string $Permissions = ''): bool
    {
        // Get the reports permissions in an array
        $Explode = array_map('trim', explode(',', $Permissions));

        // Verify the client's permission matches an array entry
        if ($this->TL->User->VerifyPermissions($Explode)) {
            return true;
        }
        return false;
    }
}