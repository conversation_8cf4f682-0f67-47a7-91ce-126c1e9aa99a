:root {
	--Neomorphic-Background: linear-gradient(-4deg, rgba(255, 255, 255, 0.88), rgba(238, 238, 238, 0.78));
	--Neomorphic-Shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.32), 0 -1px 1px 0 rgba(255, 255, 255, 1);

	--Selected-Background: linear-gradient(-32deg, #076634, #76e3a9);
	--Selected-Shadow: 0 1px 6px 0 rgba(8, 120, 52, 0.6);

	/* Input Theme Variables (light defaults) */
	--Input-Field-Background: var(--Neomorphic-Background);
	--Input-Text-Color: #181818;
	--Input-Label-Color: rgba(0, 0, 0, 0.8);
	--Input-Disabled-Background: #d0d0d0;
	--Input-Border-Color: #d2d2d2;
	--Input-Shadow: var(--Neomorphic-Shadow);

	/* Input outline variables */
	--Input-Outline-Color: transparent;
	--Input-Outline-Color-Focus: transparent;
}

:root {
	/* Date select popup variables (light defaults) */
	--Date-Select-Background: #fff;
	--Date-Select-Text-Color: #181818;
	--Date-Select-Divider-Color: rgba(0, 0, 0, 0.1);
	--Date-Select-Border-Color: var(--Input-Outline-Color);
}

#blob {
	position: absolute;
	top: 500px;
	left: 200px;
	width: 200px;
	height: 50px;
	background-color: green;
}

/*
**

/* Dark theme overrides */
body[data-theme="dark"] {
	--Neomorphic-Background: linear-gradient(-4deg, #2a2a2a, #1f1f1f);
	--Neomorphic-Shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.7), 0 -1px 1px 0 rgba(255, 255, 255, 0.05);

	--Input-Field-Background: var(--Neomorphic-Background);
	--Input-Text-Color: #f5f5f5;
	--Input-Label-Color: #a3a3a3;
	--Input-Disabled-Background: #2a2a2a;
	--Input-Border-Color: #404040;
	--Input-Outline-Color: #404040;
	--Input-Outline-Color-Focus: #a3a3a3;
	--Date-Select-Background: #2a2a2a;
	--Date-Select-Text-Color: #f5f5f5;
	--Date-Select-Divider-Color: #4a4a4a;
	--Date-Select-Border-Color: var(--Input-Outline-Color);
}

/*
**
**
**
**
**



** Input / Form Headings & Text Bodies ** ================================= */
.TL-Form-Heading {
	position: relative;
	box-sizing: border-box;
	margin: 0;
	vertical-align: top;
	width: 100%;
	height: auto;
	padding: 0 4px 0 8px;
	text-align: left;
	font-family: var(--Theme-Font);
	font-size: 28px;
	color: var(--Input-Text-Color);
	font-weight: 600;
	line-height: 32px;
}
.TL-Form-Sub-Heading {
	position: relative;
	box-sizing: border-box;
	margin: 0;
	vertical-align: top;
	width: 100%;
	height: auto;
	padding: 4px 4px 2px 8px;
	text-align: left;
	font-family: var(--Theme-Font);
	font-size: 20px;
	color: var(--Input-Text-Color);
	font-weight: 600;
	line-height: 24px;
}
.TL-Form-Text-Body {
	position: relative;
	box-sizing: border-box;
	margin: 0;
	vertical-align: top;
	width: 100%;
	height: auto;
	padding: 0 4px 4px 8px;

	/* Dark mode icon adjustments for date/time pickers */
	body[data-theme="dark"] .TL-Date-Picker > .selection-module > .selection-editor > button > img,
	body[data-theme="dark"] .TL-Time-Picker > .selection-module > .selection-editor > button > img {
		filter: invert(1) brightness(1.2);
		opacity: 0.9;
	}

	text-align: left;
}
.TL-Form-Text-Body > p {
	font-size: 16px;
	color: var(--Input-Text-Color);
	font-weight: 400;
	line-height: 24px;
}

/*
**
**
**
**
**
**
** Inputs Shared Style / Foundation
** =================================
*/

.TL-Input {
	display: inline-block;
	position: relative;
	margin: 6px 0;
	vertical-align: top;
	width: 100%;
	height: auto;
	padding: 0;
}
.TL-Input[disable-input="true"] {
	pointer-events: none !important;
	opacity: 0.75 !important;
}

/* Input Defaults Overwrite */

.TL-Input input:-webkit-autofill {
	background-color: transparent !important;
}
.TL-Input input:-moz-autofill {
	background-color: transparent !important;
}
.TL-Input input:autofill {
	background-color: transparent !important;
}

/* Input Field */

.TL-Input-Field {
	display: block;
	position: relative;
	box-sizing: border-box;
	margin: 0;
	width: 100%;
	height: 56px;
	padding: 12px 6px 0 12px;
	background: var(--Neomorphic-Background);
	text-align: left;
	font-size: 16px;
	color: var(--Input-Text-Color);
	font-weight: 500;
	letter-spacing: 0;
	border-radius: 12px;
	border: 1px solid var(--Input-Outline-Color);
	box-shadow: var(--Neomorphic-Shadow);
}

/* Focused input outline for contrast */
.TL-Input-Field:focus,
.TL-Input-Field:focus-within {
	border-color: var(--Input-Outline-Color-Focus);
	outline: none;
}
.TL-Input[disable-input="true"] .TL-Input-Field,
.TL-Input[disable-input="true"] .TL-Input-Button {
	background: var(--Input-Disabled-Background) !important;
	box-shadow: 0 0 0 0 rgba(0, 0, 0, 0) !important;
}

/* Input Label / Placeholder */

.TL-Input label {
	display: block;
	position: absolute;
	box-sizing: border-box;
	z-index: 1;
	top: 18px;
	left: 0;
	margin: 0;
	width: 100%;
	height: 20px;
	padding: 0 4px 0 12px;
	background-color: transparent;
	pointer-events: none;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	text-align: left;
	font-size: 16px;
	color: var(--Input-Label-Color);
	font-weight: 400;
	letter-spacing: 0;
	transform-origin: 12px 0;
	-webkit-transition: transform 150ms ease;
	-moz-transition: transform 150ms ease;
	transition: transform 150ms ease;
}
.TL-Input label.minimized,
.TL-Input-Field:focus ~ label,
.TL-Input-Field:not(:placeholder-shown) ~ label {
	-webkit-transform: translateY(calc(-50% - 2px)) scale(0.8);
	-moz-transform: translateY(calc(-50% - 2px)) scale(0.8);
	transform: translateY(calc(-50% - 2px)) scale(0.8);
}

/*
**
**
**
**
**
**
** Hidden Input
** =================================
*/

.TL-Input[type="hidden"] {
	width: 0;
	height: 0;
	opacity: 0;
	pointer-events: 0;
	overflow: hidden;
}

/*
**
**
**
**
**
**
** Textarea Input
** =================================
*/

.TL-Input[type="textarea"] .TL-Input-Field {
	height: 150px;
	padding-top: 26px;
	font-size: 14.5px;
	line-height: 17px;
}

/* Phone Input */

.invalid-phone {
	color: #f05227;
	font-size: 20px;
}

.TL-Phone-Input input {
	transition: color 400ms ease-in-out, font-size 400ms ease-in-out;
}

/*
**
**
**
**
**
**
** PIN Input
** =================================
*/

/* .TL-Input[type="PIN"] {
}
.TL-Input[type="PIN"] .TL-Input-Field {
    height: 64px;
    padding: 0 0 0 32px;
    text-align: center;
    font-size: 32px;
    color: #000;
    font-weight: 700;
    letter-spacing: 32px;
}

.TL-Input[type="PIN"] .windows {
    display: flex;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.TL-Input[type="PIN"] .window {
    flex: 1;
    margin: 0;
    border: 8px solid #000;
    background: transparent;
} */

/*
**
**
**
**
**
**
** Rich Text Editor
** =================================
*/
.TL-Input-Field[type="rich-text"] {
	padding: 0px;
	height: 300px;
}
.TL-Input-Field[type="rich-text"] .ql-toolbar.ql-snow {
	border-bottom: 1px solid #ccc !important;
	border-top: none !important;
	border-left: none !important;
	border-right: none !important;
}
.TL-Input-Field[type="rich-text"] .ql-container {
	border: none !important;
	height: calc(100% - 60px) !important;
	padding-bottom: 20px;
	font-weight: initial;
}
#TL-Rich-Text-Editor-Popup-Close {
	margin: 8px auto 0 auto;
	width: 100%;
	height: 52px;
}
.TL-Rich-Text-Editor-Input[data-show-label="false"] .ql-container {
	height: calc(100% - 30px) !important;
}
.TL-Rich-Text-Editor-Input[data-show-label="false"] .TL-Rich-Text-Label {
	display: none;
}
.TL-Rich-Text-Label {
	margin-top: 5px;
	padding: 8px 12px;
	font-size: 12px;
	border-bottom: 1px solid #ccc;
}

/*
**
**
**
**
**
**
** Multi-Select Input
** =================================
*/

.TL-Input[type="multiselect"] .TL-Input-Field {
	cursor: pointer;
}
.TL-Input[type="multiselect"] img.expand-state-indicator {
	pointer-events: none;
	position: absolute;
	top: 20px;
	right: 16px;
	width: 16px;
	height: 16px;
	transition: transform 400ms cubic-bezier(0.32, 0.22, 0.08, 1.22);
}
.TL-Input[type="multiselect"].options-expanded img.expand-state-indicator {
	transform: rotate(180deg);
}
.TL-Input[type="multiselect"] .results {
	position: absolute;
	box-sizing: border-box;
	top: 24px;
	left: 0;
	width: 100%;
	height: 22px;
	padding: 0 42px 0 12px;
	font-size: 16px;
	color: #181818;
	font-weight: 500;
}

/* Multi-select Option */

.TL-Input[type="multiselect"] .options {
	display: none;
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: auto;
	max-height: 230px;
	padding: 8px;
	background-color: transparent;
	overflow: auto;
}
.TL-Input[type="multiselect"].options-expanded .options {
	display: block;
}
.TL-Multiselect-Input-Option {
	position: relative;
	box-sizing: border-box;
	margin: 0 0 4px 0;
	width: 100%;
	height: 40px;
	padding: 4px;
	background-color: #fff;
	cursor: pointer;
	overflow: hidden;
	border-radius: 6px;
	box-shadow: 0 1px 3px -1px rgba(10, 10, 10, 0.28);
}
.TL-Multiselect-Input-Option::before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	margin: auto 0;
	width: 4px;
	height: calc(100% - 12px);
	border-radius: 3px;
	background-color: var(--Theme-Color);
	transform: translateX(-10px);
	-webkit-transition: transform 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
	-moz-transition: transform 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
	transition: transform 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
}
.TL-Multiselect-Input-Option[is-selected="true"]::before {
	transform: translateX(2px);
}
.TL-Multiselect-Input-Option > .display-value {
	display: inline-block;
	position: relative;
	box-sizing: border-box;
	z-index: 4;
	vertical-align: top;
	width: 100%;
	padding: 6px 36px 0 10px;
	text-align: left;
	font-size: 16px;
	color: #181818;
	font-weight: 500;
	line-height: 20px;
}
.TL-Multiselect-Input-Option .toggle-indicator {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 6px;
	box-sizing: border-box;
	margin: auto 0;
	width: 24px;
	height: 24px;
	padding: 6px 0 0 0;
	background: transparent;
	text-align: center;
	border-radius: 100%;
}
.TL-Multiselect-Input-Option .toggle-indicator > img {
	width: 12px;
	transition: transform 400ms cubic-bezier(0.32, 0.22, 0.08, 1.22);
}
.TL-Multiselect-Input-Option[is-selected="true"] .toggle-indicator > img {
	transform: rotate(45deg);
}

/*
**
**
**
**
**
**
** Multi-Select Input
** =================================
*/

.TL-Input[type="permissions"] .TL-Input-Field {
	cursor: pointer;
}
.TL-Input[type="permissions"] img.expand-state-indicator {
	pointer-events: none;
	position: absolute;
	top: 20px;
	right: 16px;
	width: 16px;
	height: 16px;
	transition: transform 400ms cubic-bezier(0.32, 0.22, 0.08, 1.22);
}
.TL-Input[type="permissions"].options-expanded img.expand-state-indicator {
	transform: rotate(180deg);
}
.TL-Input[type="permissions"] .results {
	position: absolute;
	box-sizing: border-box;
	top: 24px;
	left: 0;
	width: 100%;
	height: 22px;
	padding: 0 42px 0 12px;
	font-size: 16px;
	color: #181818;
	font-weight: 500;
}

/* Permissions Option */

.TL-Input[type="permissions"] .options {
	display: none;
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: auto;
	max-height: 600px;
	padding: 2px;
	background-color: transparent;
	overflow: auto;
	text-align: center;
}
.TL-Input[type="permissions"].options-expanded .options {
	display: block;
}
.TL-Permissions-Input-Option {
	display: inline-block;
	position: relative;
	box-sizing: border-box;
	margin: 0 0 4px 0;
	width: calc(50% - 2px);
	height: 84px;
	padding: 12px 16px 0 16px;
	background-color: #fff;
	cursor: pointer;
	overflow: hidden;
	border-radius: 6px;
	box-shadow: 0 1px 3px -1px rgba(10, 10, 10, 0.28);
	transition: background-color 180ms ease;
}
.TL-Permissions-Input-Option:nth-child(odd) {
	margin-right: 4px;
}
.TL-Permissions-Input-Option[is-selected="true"] {
	background-color: var(--Theme-Color);
}
.TL-Permissions-Input-Option > img {
	width: auto;
	height: 24px;
}
.TL-Permissions-Input-Option[is-selected="true"] > img {
	filter: invert(1);
}
.TL-Permissions-Input-Option > .display-value {
	display: inline-block;
	position: relative;
	box-sizing: border-box;
	z-index: 4;
	vertical-align: top;
	width: 100%;
	padding: 4px 0 0 0;
	text-align: center;
	font-size: 14px;
	color: #181818;
	font-weight: 500;
	line-height: 18px;
}
.TL-Permissions-Input-Option[is-selected="true"] > .display-value {
	color: #fff;
}

/*
**
**
**
**
**
**
** Radio Input
** =================================
*/

.TL-Input[type="radio"] > div {
	height: auto;
	box-sizing: border-box;
	padding: 8px 4px 4px 8px;
	background-color: #f0f0f0;
}

.TL-Input[type="radio"] label {
	position: relative;
	top: 0;
	left: 0;
	height: 32px;
	padding: 0 4px 0 4px;
}
.TL-Radio-Input-Option {
	position: relative;
	display: inline-block;
	margin: 0 22px 12px 0;
	vertical-align: top;
	width: auto;
	height: 28px;
	padding: 6px 0 0 38px;
	background: transparent;
	cursor: pointer;
	text-align: left;
}
.TL-Radio-Input-Option > .selection-indicator {
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	width: 32px;
	height: 32px;
	background: rgba(0, 0, 0, 0.05);
	overflow: hidden;
	border-radius: 100%;
	box-shadow: inset 0 1px 3px -1px rgba(0, 0, 0, 0.38);
}
.TL-Radio-Input-Option.selected > .selection-indicator {
	background: var(--Selected-Background);
	box-shadow: var(--Selected-Shadow);
}
.TL-Radio-Input-Option > .selection-indicator > img {
	position: absolute;
	pointer-events: none;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	width: 22px;
	height: 22px;
	opacity: 0;
	filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.28));
	-webkit-transform: rotate(-90deg) scale(0);
	-moz-transform: rotate(-90deg) scale(0);
	transform: rotate(-90deg) scale(0);
	-webkit-transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
	-moz-transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
	transition: transform 380ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
}
.TL-Radio-Input-Option.selected > .selection-indicator > img {
	opacity: 1;
	-webkit-transform: rotate(0deg) scale(1);
	-moz-transform: rotate(0deg) scale(1);
	transform: rotate(0deg) scale(1);
}
.TL-Radio-Input-Option > .label {
	display: inline-block;
	width: auto;
	height: 100%;
	max-width: 150px;
	font-size: 14.5px;
	color: #181818;
	font-weight: 500;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

/*
**
**
**
**
**
**
** Checkbox Input
** =================================
*/

.TL-Input[type="checkbox"] {
	cursor: pointer;
}

.TL-Input[type="checkbox"] label {
	max-width: calc(100% - 50px);
}
.TL-Input[type="checkbox"] .selection-indicator {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 8px;
	margin: auto 0;
	width: 36px;
	height: 36px;
	background: rgba(0, 0, 0, 0.05);
	overflow: hidden;
	border-radius: 8px;
	box-shadow: inset 0 1px 3px -1px rgba(0, 0, 0, 0.38);
}
.TL-Input[type="checkbox"] input[value="true"] ~ .selection-indicator {
	background: var(--Selected-Background);
	box-shadow: var(--Selected-Shadow);
}
.TL-Input[type="checkbox"] .selection-indicator > img {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	width: 22px;
	height: 22px;
	opacity: 0;
	filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.28));
	-webkit-transform: rotate(-90deg) scale(0);
	-moz-transform: rotate(-90deg) scale(0);
	transform: rotate(-90deg) scale(0);
	-webkit-transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
	-moz-transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
	transition: transform 380ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
}
.TL-Input[type="checkbox"] input[value="true"] ~ .selection-indicator > img {
	opacity: 1;
	-webkit-transform: rotate(0deg) scale(1);
	-moz-transform: rotate(0deg) scale(1);
	transform: rotate(0deg) scale(1);
}

/*
**
**
**
**
**
**
** Content Checkbox Input
** =================================
*/

.TL-Input[type="content-checkbox"] {
	height: auto;
	cursor: pointer;
}
.TL-Input[type="content-checkbox"] > .TL-Input-Field {
	height: auto;
	min-height: 56px;
}
.TL-Input[type="content-checkbox"] label {
	position: relative;
	top: 0;
	margin: 0;
	max-width: calc(100% - 50px);
	height: auto;
	padding: 0 0 12px 0;
	white-space: wrap;
	text-overflow: initial;
}
.TL-Input[type="content-checkbox"] label > span {
	display: block;
}
.TL-Input[type="content-checkbox"] label > span.heading {
	font-size: 16px;
	font-weight: 500;
	line-height: 20px;
}
.TL-Input[type="content-checkbox"] label > span.description {
	padding-top: 4px;
	font-size: 14px;
	font-weight: 400;
	line-height: 18px;
}
.TL-Input[type="content-checkbox"] .selection-indicator {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 8px;
	margin: auto 0;
	width: 36px;
	height: 36px;
	background: rgba(0, 0, 0, 0.05);
	overflow: hidden;
	border-radius: 8px;
	box-shadow: inset 0 1px 3px -1px rgba(0, 0, 0, 0.38);
}
.TL-Input[type="content-checkbox"] input[value="true"] ~ .selection-indicator {
	background: var(--Selected-Background);
	box-shadow: var(--Selected-Shadow);
}
.TL-Input[type="content-checkbox"] .selection-indicator > img {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	width: 22px;
	height: 22px;
	opacity: 0;
	filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.28));
	-webkit-transform: rotate(-90deg) scale(0);
	-moz-transform: rotate(-90deg) scale(0);
	transform: rotate(-90deg) scale(0);
	-webkit-transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
	-moz-transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
	transition: transform 380ms cubic-bezier(0.32, 0.22, 0.08, 1.18), opacity 180ms ease;
}
.TL-Input[type="content-checkbox"] input[value="true"] ~ .selection-indicator > img {
	opacity: 1;
	-webkit-transform: rotate(0deg) scale(1);
	-moz-transform: rotate(0deg) scale(1);
	transform: rotate(0deg) scale(1);
}

/*
**
**
**
**
**
**
** Content Checkbox Input
** =================================
*/
.TL-Input[type="switch"] .switch-container {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 8px;
	display: flex;
	align-items: center;
}

.TL-Input[type="switch"] .switch-wrapper {
	position: relative;
	width: 55px;
	height: 30px;
	box-shadow: inset 0 1px 3px -1px rgba(0, 0, 0, 0.38);
	border-radius: 25px;
	box-sizing: border-box;
	background: rgba(0, 0, 0, 0.05);
}

.TL-Input[type="switch"] .switch {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	margin: 3px 4px;
	height: 80%;
	width: 45%;
	border-radius: 100px;
	padding: 0;
	background: white;
	transition: all 0.3s ease;
	box-shadow: inset 1px 1px 2px rgba(255, 255, 255, 0.3), inset -1px -1px 2px rgba(0, 0, 0, 0.15), 0 2px 3px rgba(50, 50, 93, 0.2), 0 1px 2px rgba(0, 0, 0, 0.2);
}

.switch-input input[value="true"] ~ .switch-container .switch-wrapper .switch {
	left: 40%;
}

.switch-input input[value="true"] ~ .switch-container .switch-wrapper {
	background: linear-gradient(-4deg, #939cff, #896ef5);
}

.TL-Input[type="switch"] input {
	display: none;
}

/*
**
**
**
**
**
**
** Date Input
** =================================
*/

.TL-Price-Input {
	position: relative;
}
.TL-Price-Input span.hidden {
	display: none;
}

.TL-Price-Input span {
	position: absolute;
	left: 10;
	top: 11;
	bottom: 0;
	font-size: 18px;
	display: flex;
	align-items: center;
	color: #181818;
	z-index: 10;
}

.TL-Price-Input input {
	padding-left: 25px;
}

/*
**
**
**
**
**
**
** Percentage Input
** =================================
*/

.TL-Percentage-Input span {
	position: absolute;
	right: 20;
	top: 11;
	bottom: 0;
	font-size: 18px;
	display: flex;
	align-items: center;
	color: #181818;
	z-index: 10;
	font-weight: 500;
}

.TL-Percentage-Input span.hidden {
	display: none;
}

.TL-Percentage-Input input {
	padding-right: 50px;
}

/* PIN Input */

.pin-wrapper {
	display: relative;
}

.digit-container {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 2%;
	width: 100%;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
}

.TL-Input-Field.digit {
	box-shadow: none;
}

.digit {
	font-size: 18px !important;
	height: 75% !important;
	width: 10%;
	padding: 10px 14px 10px 14px !important;
	border: 2px solid lightgray;
	color: var(--Theme-Color-Dark) !important;
}

.digit:not([data-digit=""]) {
	border: 2px solid var(--Theme-Color);
}

.PIN-Input input[type="number"] {
	background-color: transparent;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 99;
	opacity: 0;
}

.PIN-Input > .TL-Input-Field {
	padding: 0 !important;
}

.digit.active {
	border: 2px solid var(--Theme-Color);
	box-shadow: 0 0 8px rgba(96, 82, 255, 0.3), 0 0 10px rgba(96, 82, 255, 0.2);
}

/*
**
**
**
**
**
**
** Date Input
** =================================
*/

.TL-Input[type="date"]:not(.expanded) {
	cursor: pointer;
}
.TL-Input[type="date"].expanded {
	z-index: 10;
}
.TL-Date-Picker {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	width: 180px;
	height: 100%;
	-webkit-transition: width 180ms ease, height 180ms ease;
	-moz-transition: width 180ms ease, height 180ms ease;
	transition: width 180ms ease, height 180ms ease;
}
.TL-Input[type="date"].expanded .TL-Date-Picker {
	top: -20%;
	width: 200px;
	height: 140%;
	background-color: var(--Date-Select-Background);
	overflow: hidden;
	border-radius: 8px;
	border: 1px solid var(--Date-Select-Border-Color);
	box-shadow: 0 8px 22px -1px rgba(0, 0, 0, 0.28);
}
.TL-Date-Picker > .selection-module {
	position: absolute;
	top: 0;
	margin: auto;
	height: 100%;
}
.TL-Date-Picker > .selection-module[selection="month"] {
	left: 0;
	width: 32%;
}
.TL-Date-Picker > .selection-module[selection="day"] {
	left: 32%;
	width: 32%;
}
.TL-Date-Picker > .selection-module[selection="year"] {
	right: 0;
	width: 36%;
}
.TL-Date-Picker > .selection-module[selection="day"]::before,
.TL-Date-Picker > .selection-module[selection="day"]::after {
	content: "";
	z-index: 5;
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	margin: auto 0;
	width: 2px;
	height: calc(100% - 16px);
	background-color: var(--Date-Select-Divider-Color);
	pointer-events: none;
}
.TL-Date-Picker > .selection-module[selection="day"]::before {
	left: -2px;
}
.TL-Date-Picker > .selection-module[selection="day"]::after {
	right: -2px;
}
.TL-Date-Picker > .selection-module > .display-selection {
	display: none;
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	padding: 18px 0 0 0;
	text-align: center;
	transition: color 500ms ease;
}
.TL-Input[type="date"].invalid-date .display-selection {
	color: #f05227;
}
.TL-Date-Picker > .selection-module > .selection-editor {
	display: none;
	position: relative;
	width: 100%;
	height: 100%;
}
.TL-Date-Picker > .selection-module > .selection-editor > button {
	position: absolute;
	box-sizing: border-box;
	width: 100%;
	height: 22px;
	padding: 0;
	background: var(--Date-Select-Background);
	text-align: center;
}
.TL-Date-Picker > .selection-module > .selection-editor > button.up {
	top: 0;
}
.TL-Date-Picker > .selection-module > .selection-editor > button.down {
	bottom: 0;
}
.TL-Date-Picker > .selection-module > .selection-editor > button > img {
	width: 16px;
	height: auto;
	opacity: 0.5;
}
.TL-Date-Picker > .selection-module > .selection-editor > button:hover > img {
	opacity: 1;
}
.TL-Date-Picker > .selection-module > .selection-editor > input[type="number"] {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	margin: auto 0;
	width: 100%;
	height: 34px;
	text-align: center;
	background: var(--Date-Select-Background);
	font-size: 16px;
	color: var(--Date-Select-Text-Color);
	font-weight: 500;
}
.TL-Input[type="date"].expanded .TL-Date-Picker > .selection-module > .selection-editor,
.TL-Input[type="date"]:not(.expanded) .TL-Date-Picker > .selection-module > .display-selection {
	display: block;
}

/*
**
**
**
**
**
**
** Time Input
** =================================
*/

.TL-Input[type="time"]:not(.expanded) {
	cursor: pointer;
}
.TL-Input[type="time"].expanded {
	z-index: 10;
}
.TL-Time-Picker {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 0;
	width: 180px;
	height: 100%;
	-webkit-transition: width 180ms ease, height 180ms ease;
	-moz-transition: width 180ms ease, height 180ms ease;
	transition: width 180ms ease, height 180ms ease;
}
.TL-Input[type="time"].expanded .TL-Time-Picker {
	top: -20%;
	width: 200px;
	height: 140%;
	background-color: #fff;
	overflow: hidden;
	border-radius: 8px;
	box-shadow: 0 8px 22px -1px rgba(0, 0, 0, 0.28);
}
.TL-Time-Picker > .selection-module {
	position: absolute;
	top: 0;
	margin: auto;
	height: 100%;
}
.TL-Time-Picker > .selection-module[selection="hour"] {
	left: 0;
	width: 35%;
}
.TL-Time-Picker > .selection-module[selection="minute"] {
	left: 35%;
	width: 35%;
}
.TL-Time-Picker > .selection-module[selection="hour"]::after {
	content: "";
	z-index: 5;
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	margin: auto 0;
	width: 2px;
	height: calc(100% - 16px);
	background-color: rgba(0, 0, 0, 0.1);
	pointer-events: none;
}
.TL-Time-Picker > .selection-module[selection="hour"]::after {
	right: -2px;
}
.TL-Time-Picker > .selection-module > .display-selection {
	display: none;
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	padding: 18px 0 0 0;
	text-align: center;
	transition: color 500ms ease;
}
.TL-Input[type="time"].invalid-time .display-selection {
	color: #f05227;
}
.TL-Time-Picker > .selection-module > .selection-editor {
	display: none;
	position: relative;
	width: 100%;
	height: 100%;
}
.TL-Time-Picker > .selection-module > .selection-editor > button {
	position: absolute;
	box-sizing: border-box;
	width: 100%;
	height: 22px;
	padding: 0;
	background: #fff;
	text-align: center;
}
.TL-Time-Picker > .selection-module > .selection-editor > button.up {
	top: 0;
}
.TL-Time-Picker > .selection-module > .selection-editor > button.down {
	bottom: 0;
}
.TL-Time-Picker > .selection-module > .selection-editor > button > img {
	width: 16px;
	height: auto;
	opacity: 0.5;
}
.TL-Time-Picker > .selection-module > .selection-editor > button:hover > img {
	opacity: 1;
}
.TL-Time-Picker > .meridiem-module {
	position: absolute;
	top: 0;
	right: 0;
	width: 28%;
	height: 100%;
}
.TL-Time-Picker > .meridiem-module > button {
	position: relative;
	box-sizing: border-box;
	margin: 6px auto 0 auto;
	width: calc(100% - 12px);
	height: 22px;
	padding: 0;
	background: #e0e0e0;
	text-align: center;
	border-radius: 4px;
	text-align: center;
	font-size: 14px;
	color: #828282;
	font-weight: 500;
	overflow: hidden;
}
.TL-Input[type="time"].expanded .TL-Time-Picker > .meridiem-module > button {
	height: 28px;
	border-radius: 6px;
}
.TL-Time-Picker > .meridiem-module > button > span {
	position: relative;
	z-index: 5;
}
.TL-Time-Picker > .meridiem-module > button.am {
	margin-top: 3px;
}
.TL-Input[type="time"].expanded .TL-Time-Picker > .meridiem-module > button.am {
	margin-top: 10px;
}
.TL-Time-Picker > .meridiem-module > button.pm {
	margin-top: 6px;
}
.TL-Input[type="time"] .TL-Time-Picker > .meridiem-module > button.am[selected-value="am"] {
	background: linear-gradient(160deg, #abe4ff, #5ba5de);
	color: #fff;
}
.TL-Input[type="time"] .TL-Time-Picker > .meridiem-module > button.am[selected-value="am"]::before {
	content: "";
	display: block;
	position: absolute;
	top: -4px;
	left: -4px;
	width: 16px;
	height: 16px;
	background: radial-gradient(#ffe174, #ffcc74);
	border-radius: 100%;
	filter: blur(2px);
}
.TL-Input[type="time"] .TL-Time-Picker > .meridiem-module > button.pm[selected-value="pm"] {
	background: linear-gradient(160deg, #5e43ce, #220076);
	color: #fff;
}
.TL-Input[type="time"] .TL-Time-Picker > .meridiem-module > button.pm[selected-value="pm"]::before {
	content: "";
	display: block;
	position: absolute;
	z-index: 1;
	top: -2px;
	left: -2px;
	width: 16px;
	height: 16px;
	background: transparent;
	border-radius: 100%;
	box-shadow: inset 0px 5px #f0f0f0, inset 0px 5px 1px 1px #f0f0f0;
	-moz-box-shadow: inset 0px 5px #f0f0f0, inset 0px 5px 1px 1px #f0f0f0;
	transform: rotate(-60deg);
}
.TL-Input[type="time"].expanded .TL-Time-Picker > .meridiem-module > button.pm[selected-value="pm"]::before {
	top: 0px;
	left: -1px;
}
.TL-Time-Picker > .selection-module > .selection-editor > input[type="number"] {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	margin: auto 0;
	width: 100%;
	height: 34px;
	background: #fff;
	text-align: center;
	font-size: 16px;
	color: #181818;
	font-weight: 500;
}
.TL-Input[type="time"].expanded .TL-Time-Picker > .selection-module > .selection-editor,
.TL-Input[type="time"]:not(.expanded) .TL-Time-Picker > .selection-module > .display-selection {
	display: block;
}

/*
**
**
**
**
**
**
** Signature Input
** =================================
*/

.TL-Input[type="signature"] > .TL-Input-Field {
	height: 108px;
}
.TL-Input[type="signature"] label {
	top: 8px;
}

/* Signature Steps */

.TL-Input[type="signature"] .steps {
	position: relative;
	margin: 22px auto 0 auto;
	width: 100%;
	height: 56px;
	padding: 0;
	background: rgba(0, 0, 0, 0.05);
	overflow: hidden;
	border-radius: 8px;
	box-shadow: inset 0 1px 3px -1px rgba(0, 0, 0, 0.38);
}
.TL-Input[type="signature"][step="signed"] .steps {
	display: none;
}
.TL-Input[type="signature"] .steps .signature-input {
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	width: 100%;
	height: 100%;
	-webkit-transition: top 280ms cubic-bezier(0.32, 0.22, 0.08, 1.32), opacity 280ms ease;
	-moz-transition: top 280ms cubic-bezier(0.32, 0.22, 0.08, 1.32), opacity 280ms ease;
	transition: top 280ms cubic-bezier(0.32, 0.22, 0.08, 1.32), opacity 280ms ease;
}
.TL-Input[type="signature"] .steps .signature-input > input {
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	padding: 0 6px 0 12px;
	background: transparent;
	text-align: left;
	font-size: 14.5px;
	color: #181818;
	font-weight: 500;
}
.TL-Input[type="signature"][step="password"] .steps .signature-input.username {
	top: -100%;
}
.TL-Input[type="signature"] .steps .signature-input.password {
	top: 100%;
}
.TL-Input[type="signature"][step="password"] .steps .signature-input.password {
	top: 0;
}
.TL-Input[type="signature"] .steps > button.TL-Signature-Input-Next-Step {
	position: absolute;
	z-index: 4;
	box-sizing: border-box;
	top: 0;
	bottom: 0;
	right: 8px;
	margin: auto 0;
	width: 38px;
	height: 38px;
	padding: 0;
	background-color: #5442e3;
	animation: SignatureNextStepButton 2s infinite;
	text-align: center;
	border-radius: 10px;
	box-shadow: 0 1px 2px 0 rgba(30, 10, 100, 0.36);
}
@keyframes SignatureNextStepButton {
	0% {
		background-color: #5442e3;
	}
	50% {
		background-color: #b5abff;
		box-shadow: 0 2px 12px 0 rgba(74, 58, 210, 0.5);
	}
	100% {
		background-color: #5442e3;
	}
}
.TL-Input[type="signature"] .steps > button.TL-Signature-Input-Next-Step > img {
	width: 18px;
	height: auto;
}
/* Signature Summary */

.TL-Input[type="signature"] .signature-summary {
	display: none;
	position: relative;
	margin: 26px auto 0 auto;
	width: 100%;
	height: 68px;
	text-align: center;
}
.TL-Input[type="signature"][step="signed"] .signature-summary {
	display: block;
}
.TL-Input[type="signature"] .signature-summary > button.reset {
	position: absolute;
	top: -32px;
	right: 6px;
	width: 26px;
	height: 26px;
	background: transparent;
	text-align: center;
}
.TL-Input[type="signature"] .signature-summary > button.reset > img {
	width: 20px;
	opacity: 0.65;
	transition: transform 600ms cubic-bezier(0.32, 0.22, 0.08, 1.22);
}
.TL-Input[type="signature"] .signature-summary > button.reset:hover > img {
	opacity: 1;
	transform: rotate(360deg);
}
.TL-Input[type="signature"] .signature-summary > span.name {
	display: inline-block;
	position: relative;
	vertical-align: top;
	width: 100%;
	height: auto;
	padding: 0;
	text-align: left;
	text-transform: capitalize;
	font-family: var(--Cursive-Font);
	font-size: 28px;
	color: #28282a;
	letter-spacing: 0;
	line-height: 32px;
}
.TL-Input[type="signature"] .signature-summary > span.date-signed {
	display: inline-block;
	vertical-align: top;
	width: 100%;
	height: auto;
	padding: 0;
	text-align: left;
	font-size: 13.5px;
	color: #626262;
	font-weight: 400;
	letter-spacing: 0;
}

/*
**
**
**
**
**
**
** User Picker Input
** =================================
*/

.TL-Input[type="user"] > .search-bar {
	position: relative;
	width: 100%;
}
.TL-Input[type="user"] .search-results,
.TL-Input[type="user"] .selected-users {
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: auto;
	max-height: 230px;
	padding: 8px;
	background-color: transparent;
	overflow: auto;
}
.TL-Input[type="user"]:not(.active-search) .search-results,
.TL-Input[type="user"].active-search .selected-users {
	display: none;
}

/* User Result */

.TL-User-Input-Value {
	position: relative;
	box-sizing: border-box;
	margin: 0 0 8px 0;
	width: 100%;
	height: 60px;
	padding: 8px 0 0 52px;
	background-color: #fff;
	cursor: pointer;
	text-align: left;
	border-radius: 12px;
	box-shadow: 0 1px 3px 0 rgba(10, 10, 10, 0.18);
}
.TL-User-Input-Value[selected="true"] {
	border: 2px solid #ddd;
	box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}
.TL-User-Input-Value > figure {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 8px;
	margin: auto 0;
	width: 38px;
	height: 38px;
	padding: 0;
	background-color: #fff;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
	border-radius: 100%;
}
.TL-User-Input-Value > .full-name {
	display: inline-block;
	vertical-align: top;
	width: calc(100% - 60px);
	padding: 0;
	text-align: left;
	font-size: 16.5px;
	color: #181818;
	font-weight: 500;
	line-height: 18px;
}
.TL-User-Input-Value > .data-points {
	position: relative;
	width: 100%;
	height: auto;
	padding: 4px 0;
	text-align: left;
}
.TL-User-Input-Value > .data-points > .data-point {
	display: inline-block;
	margin: 0 4px 0 0;
	vertical-align: top;
	width: auto;
	min-width: 20px;
	height: 16px;
	padding: 2px 7px 0 7px;
	background-color: #9077ff;
	text-align: center;
	font-size: 12px;
	color: #fff;
	font-weight: 400;
	border-radius: 5px;
}
.TL-User-Input-Value .toggle-indicator {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 12px;
	margin: auto 0;
	width: 16px;
	height: 16px;
	background: transparent;
	text-align: center;
}
.TL-User-Input-Value .toggle-indicator > img {
	width: 100%;
}
.TL-User-Input-Value[selected="true"] .toggle-indicator > img {
	transform: rotate(45deg);
}

/*
**
**
**
**
**
**
** Store Picker Input
** =================================
*/

.TL-Input[type="store"] > .search-bar {
	position: relative;
	width: 100%;
}
.TL-Input[type="store"] .reveal-expanded-store-selection {
	display: none;
	position: absolute;
	top: 16px;
	right: 8px;
	width: 22px;
	height: 22px;
	opacity: 0.65;
	cursor: pointer;
}
.TL-Input[type="store"][allow-multiple="true"] .reveal-expanded-store-selection {
	display: block;
}
.TL-Input[type="store"] .reveal-expanded-store-selection:hover {
	transform: scale(1.1);
	opacity: 1;
}
.TL-Input[type="store"] .search-results,
.TL-Input[type="store"] .selected-stores {
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: auto;
	max-height: 230px;
	padding: 8px;
	background-color: transparent;
	overflow: auto;
}
.TL-Input[type="store"]:not(.active-search) .search-results,
.TL-Input[type="store"].active-search .selected-stores {
	display: none;
}

/* User Result */

.TL-Store-Input-Value {
	position: relative;
	box-sizing: border-box;
	margin: 0 0 8px 0;
	width: 100%;
	height: 60px;
	padding: 8px 0 0 8px;
	background-color: #888;
	border: 3px solid #fff;
	cursor: pointer;
	overflow: hidden;
	border-radius: 12px;
	box-shadow: 0 1px 3px 0 rgba(10, 10, 10, 0.18);
}
.TL-Store-Input-Value[selected="true"] {
	border-color: #34e182;
	box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}
.TL-Store-Input-Value > figure {
	position: absolute;
	top: 0;
	left: 0;
	margin: auto 0;
	width: 100%;
	height: 100%;
	padding: 0;
	background-color: #fff;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
	filter: brightness(65%);
	border-radius: 6px;
}
.TL-Store-Input-Value:not([selected="true"]) > figure {
	filter: blur(6px) brightness(75%);
	opacity: 0.5;
}
.TL-Store-Input-Value > .full-name {
	display: inline-block;
	position: relative;
	z-index: 4;
	vertical-align: top;
	width: calc(100% - 60px);
	padding: 5px 0 0 0;
	text-align: left;
	font-family: var(--Theme-Font);
	font-size: 28px;
	color: rgba(255, 255, 255, 0.92);
	font-weight: 600;
	line-height: 32px;
	text-shadow: 0 1px 8px rgba(0, 0, 0, 0.52);
}
.TL-Store-Input-Value .toggle-indicator {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 12px;
	box-sizing: border-box;
	margin: auto 0;
	width: 32px;
	height: 32px;
	padding: 6px 0 0 0;
	background: #000;
	border: 2px solid #555;
	text-align: center;
	border-radius: 100%;
}
.TL-Store-Input-Value .toggle-indicator {
	background-color: #000;
}
.TL-Store-Input-Value .toggle-indicator > img {
	width: 16px;
	transition: transform ease 120ms;
}
.TL-Store-Input-Value[selected="true"] .toggle-indicator > img {
	transform: rotate(45deg);
}

/* Expanded Store Selection */

.TL-Expanded-Store-Selection-Wrapper {
	position: fixed;
	z-index: 26;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: rgba(10, 10, 10, 0.6);
	backdrop-filter: blur(3px);
}
.TL-Expanded-Store-Selection-Wrapper > .selection-aside {
	position: absolute;
	box-sizing: border-box;
	top: 0;
	right: 0;
	width: 100%;
	max-width: 600px;
	height: 100%;
	padding: 16px;
	background-color: #f8f8f8;
	overflow: auto;
}
.TL-Expanded-Store-Selection-Wrapper > .selection-aside > header {
	position: relative;
	width: 100%;
	height: 50px;
	background: transparent;
	border-bottom: 1px solid #e2e2e2;
	text-align: left;
}
.TL-Expanded-Store-Selection-Wrapper > .selection-aside > header > h1 {
	padding: 4px 0 0 0;
	font-size: 28px;
	color: #000;
	font-weight: 700;
	line-height: 32px;
}
.TL-Expanded-Store-Selection-Wrapper > .selection-aside > header > button.close-store-expanded-selection {
	position: absolute;
	top: 0;
	right: 0;
	width: 32px;
	height: 32px;
	background-color: #fff;
	border-radius: 100%;
	box-shadow: 0 1px 3px 0 rgba(10, 10, 10, 0.18);
}
.TL-Expanded-Store-Selection-Wrapper > .selection-aside > header > button.close-store-expanded-selection > img {
	width: 100%;
}
.TL-Expanded-Store-Selection-Wrapper > .selection-aside > .population {
	padding: 0 0 32px 0;
}

/* Expanded Stores Group Header */

header.Expanded-Stores-Group-Header {
	position: relative;
	box-sizing: border-box;
	margin: 32px 0 8px 0;
	width: 100%;
	height: 44px;
	padding: 6px 0 0 42px;
	background-color: #000;
	text-align: left;
	border-radius: 8px;
}
header.Expanded-Stores-Group-Header > h1 {
	font-size: 28px;
	color: #fff;
	font-weight: 500;
	line-height: 32px;
}
header.Expanded-Stores-Group-Header > .selection-box {
	position: absolute;
	top: 6px;
	left: 4px;
	width: 26px;
	height: 26px;
	background-color: #e2e2e2;
	cursor: pointer;
	border-radius: 6px;
	box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.26);
}
header.Expanded-Stores-Group-Header > .selection-box:checked {
	background-color: #17cb64;
	background-image: url("../resources/icons/check.white.png");
	background-repeat: no-repeat;
	background-position: center;
	background-size: 16px 16px;
	box-shadow: 0 1px 2px 0 rgba(0, 100, 30, 0.16);
}

/*
**
**
**
**
**
**
** Address Input
** =================================
*/
.TL-Input[type="address"] button.toggle-sub-fields {
	display: block;
	position: relative;
	z-index: 5;
	margin: 0 0 16px 0;
	/* vertical-align: top; */
	width: 100%;
	height: 32px;
	padding: 2px 10px;
	background: linear-gradient(160deg, #8e98ff, #8577f9);
	text-align: center;
	font-size: 12.5px;
	color: #fff;
	font-weight: 500;
	letter-spacing: 0.2px;
	border-radius: 0 0 6px 6px;
	box-shadow: 0 1px 2px 0 rgba(30, 10, 100, 0.24);
}
.TL-Input[type="address"] button.show-on-map {
	width: 100%;
}
.TL-Input[type="address"]:not(.expanded) button.show-on-map {
	display: none;
}
.TL-Input[type="address"] button.show-on-map > img {
	width: 18px;
}

.TL-Input[type="address"].expanded button.toggle-sub-fields > span.expand {
	display: none;
}
.TL-Input[type="address"]:not(.expanded) button.toggle-sub-fields > span.collapse {
	display: none;
}

.TL-Input[type="address"] {
	-webkit-transition: padding 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
	-moz-transition: padding 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
	transition: padding 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
}
.TL-Input[type="address"].expanded {
	box-sizing: border-box;
	padding: 12px;
	background-color: #e8e8e8;
	border: 2px solid #d2d2d2;
	border-radius: 8px;
}
.TL-Input[type="address"]:not(.verified) input.address-autofill-input {
	color: #dc4836;
}
.TL-Input[type="address"]::after {
	content: "⚠️";
	display: block;
	position: absolute;
	top: 2px;
	right: 10px;
	width: 14px;
	height: 14px;
	text-align: center;
	font-size: 14px;
}
.TL-Input[type="address"].verified::after {
	content: "✅";
}
.TL-Input[type="address"] .address-input {
	position: relative;
	margin: 0 0 8px 0;
}
.TL-Input[type="address"] .address-input:first-child {
	margin: 0;
}
.TL-Input[type="address"] input.address-autofill-input {
	margin: 0;
	border-radius: 6px 6px 0 0;
}
.TL-Input[type="address"] .sub-field {
	display: inline-block;
	width: calc(50% - 3px);
}
.TL-Input[type="address"] .sub-field:nth-child(even) {
	margin-left: 6px;
}
.TL-Input[type="address"]:not(.expanded) .sub-field {
	display: none;
}

/*
**
**
**
**
**
**
** File Upload Input
** =================================
*/

.TL-File-Upload {
	position: relative;
	box-sizing: border-box;
	margin: 0;
	width: 100%;
	height: 100%;
	padding: 0;
	background-color: transparent;
	border: 2px dashed var(--Input-Border-Color);
	overflow: hidden;
	border-radius: 12px;
}
.content-window .TL-File-Upload {
	height: 250px;
}
.TL-File-Upload.upload-in-progress {
	pointer-events: none;
	opacity: 0.5;
}
.TL-File-Upload.upload-successful {
	border-color: #00af13;
	background-color: #e9ffeb;
}
.TL-File-Upload > figure {
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
.TL-File-Upload > figure > img {
	width: 100%;
	height: 100%;
	object-fit: contain;
	object-position: center;
}
.TL-File-Upload > label {
	display: block;
	pointer-events: none;
	position: absolute;
	z-index: 6;
	top: 6%;
	left: 0;
	right: 0;
	margin: 0 auto;
	width: 92%;
	height: auto;
	padding: 0;
	background-color: transparent;
	text-align: center;
	border-radius: 3px;
}
.TL-File-Upload > label > span {
	display: block;
	width: 100%;
	text-align: center;
}
.TL-File-Upload > label > span.heading {
	font-size: 18px;
	color: #181818;
	font-weight: 700;
	letter-spacing: 0;
	line-height: 22px;
}
.TL-File-Upload > label > span.file-name {
	box-sizing: border-box;
	padding: 4px 12px;
	/* background-color: #bbf1bb; */
	font-size: 18px;
	color: #0f490f;
	font-weight: 500;
	letter-spacing: 0;
	line-height: 22px;
	border-radius: 6px;
}
.TL-File-Upload > input[type="file"] {
	position: absolute;
	z-index: 4;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
}

/*
**
**
**
**
**
**
** Photo Input
** =================================
*/

.TL-Input-photo {
	position: relative;
	box-sizing: border-box;
	margin: 0;
	width: 100%;
	height: 100%;
	max-height: 300px;
	padding: 0;
	background-color: transparent;
	border: 2px dashed var(--Input-Border-Color);
	overflow: hidden;
	border-radius: 12px;
}
.content-window .TL-Input-photo {
	height: 250px;
}
.TL-Input-photo.upload-in-progress {
	pointer-events: none;
	opacity: 0.5;
}
.TL-Input-photo.upload-successful {
	border-color: #00af13;
	background-color: #e9ffeb;
}
.TL-Input-photo > figure {
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
.TL-Input-photo > figure > img {
	width: 100%;
	height: 100%;
	object-fit: contain;
	object-position: center;
}
.TL-Input-photo > label {
	display: block;
	pointer-events: none;
	position: absolute;
	z-index: 6;
	top: 6%;
	left: 0;
	right: 0;
	margin: 0 auto;
	width: 92%;
	height: auto;
	padding: 0;
	background-color: transparent;
	text-align: center;
	border-radius: 3px;
}
.TL-Input-photo > label > span {
	display: block;
	width: 100%;
	text-align: center;
}
.TL-Input-photo > label > span.heading {
	font-size: 18px;
	color: #181818;
	font-weight: 700;
	letter-spacing: 0;
	line-height: 22px;
}
.TL-Input-photo > label > span.file-name {
	box-sizing: border-box;
	padding: 4px 12px;
	/* background-color: #bbf1bb; */
	font-size: 18px;
	color: #0f490f;
	font-weight: 500;
	letter-spacing: 0;
	line-height: 22px;
	border-radius: 6px;
}
.TL-Input-photo > input[type="file"] {
	position: absolute;
	z-index: 4;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
}

/* search select */
.TL-Input[type="searchselect"] {
	box-sizing: border-box;
}

.TL-Input[type="searchselect"] * {
	box-sizing: border-box;
}

.TL-Input[type="searchselect"] .search-results {
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: auto;
	max-height: 230px;
	padding: 0px 8px;
	background-color: transparent;
	overflow: auto;
	margin-top: 8px;
}

.no-results.hidden {
	display: none;
}

[type="searchselect"] .searchselect-selected {
	margin-top: 8px;
	padding: 0px 8px;
	display: flex;
	gap: 4px;
	flex-wrap: wrap;
	min-height: 21px;
	transition: min-height 150ms ease;
}

[type="searchselect"] .searchselect-selected.hidden {
	min-height: 0px;
}

[type="searchselect"] .searchselect-selected .selected-button {
	font-size: 12px;
	padding: 0.25em 0.4em;
	background-color: var(--Theme-Color);
	border-radius: 2px;
	color: white;
	padding-left: 0.4em;
}

[type="searchselect"] .searchselect-selected .selected-button:hover {
	background-color: #5448dd;
}

[type="searchselect"] .searchselect-selected .selected-button.hidden {
	display: none;
}

.TL-Searchselect-Input-Option.no-bg {
	position: relative;
	box-sizing: border-box;
	margin: 0 0 4px 0;
	width: 100%;
	height: 40px;
	padding: 4px;
	background-color: #fff;
	cursor: pointer;
	overflow: hidden;
	border-radius: 6px;
	box-shadow: 0 1px 3px -1px rgba(10, 10, 10, 0.28);
}

.TL-Searchselect-Input-Option.hidden {
	display: none !important;
}

.TL-Searchselect-Input-Option.no-bg::before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	margin: auto 0;
	width: 4px;
	height: calc(100% - 12px);
	border-radius: 3px;
	background-color: var(--Theme-Color);
	transform: translateX(-10px);
	-webkit-transition: transform 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
	-moz-transition: transform 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
	transition: transform 220ms cubic-bezier(0.28, 0.28, 0.08, 1.12);
}
.TL-Searchselect-Input-Option.no-bg[is-selected="true"]::before {
	transform: translateX(2px);
}
.TL-Searchselect-Input-Option.no-bg > .display-value {
	display: inline-block;
	position: relative;
	box-sizing: border-box;
	z-index: 4;
	vertical-align: top;
	width: 100%;
	padding: 6px 36px 0 10px;
	text-align: left;
	font-size: 16px;
	color: #181818;
	font-weight: 500;
	line-height: 20px;
}
.TL-Searchselect-Input-Option.no-bg .toggle-indicator {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 6px;
	box-sizing: border-box;
	margin: auto 0;
	width: 24px;
	height: 24px;
	padding: 6px 0 0 0;
	background: transparent;
	text-align: center;
	border-radius: 100%;
}
.TL-Searchselect-Input-Option.no-bg .toggle-indicator > img {
	width: 12px;
	transition: transform 400ms cubic-bezier(0.32, 0.22, 0.08, 1.22);
}
.TL-Searchselect-Input-Option.no-bg[is-selected="true"] .toggle-indicator > img {
	transform: rotate(45deg);
}

.TL-Searchselect-Input-Option.bg {
	position: relative;
	box-sizing: border-box;
	margin: 0 0 8px 0;
	width: 100%;
	height: 60px;
	padding: 8px 0 0 8px;
	background-color: #fff;
	cursor: pointer;
	border-radius: 12px;
	box-shadow: 0 1px 3px 0 rgba(10, 10, 10, 0.18);
	padding: 1em;
	display: flex;
	gap: 1em;
	align-items: center;
	font-size: 16px;
	text-align: left;
}

.TL-Searchselect-Input-Option.bg .primary-value {
	font-weight: 500;
}
.TL-Searchselect-Input-Option.bg .subtitle {
	font-size: 0.75em;
}

.TL-Searchselect-Input-Option.bg[is-selected="true"] {
	outline: 3px solid var(--Theme-Color);
	outline-offset: -3px;
}
.TL-Searchselect-Input-Option.bg > img {
	height: 42px;
	width: 42px;
	object-fit: cover;
	border-radius: 100%;
	overflow: hidden;
	background-color: #d0d0d0;
}
.TL-Searchselect-Input-Option.bg > .full-name {
	display: inline-block;
	position: relative;
	z-index: 4;
	vertical-align: top;
	width: calc(100% - 60px);
	padding: 5px 0 0 0;
	text-align: left;
	font-family: var(--Theme-Font);
	font-size: 28px;
	color: rgba(255, 255, 255, 0.92);
	font-weight: 600;
	line-height: 32px;
	text-shadow: 0 1px 8px rgba(0, 0, 0, 0.52);
}
.TL-Searchselect-Input-Option.bg .toggle-indicator {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 12px;
	box-sizing: border-box;
	margin: auto 0;
	width: 32px;
	height: 32px;
	padding: 6px 0 0 0;
	text-align: center;
	border-radius: 100%;
}
.TL-Searchselect-Input-Option.bg .toggle-indicator > img {
	width: 20px;
}
.TL-Searchselect-Input-Option.bg .toggle-indicator > img {
	transition: transform 150ms ease;
}
.TL-Searchselect-Input-Option.bg[is-selected="true"] .toggle-indicator > img {
	transform: rotate(45deg);
}

/*
**
**
**
**
**
**
** Tags Input
** =================================
*/

.TL-Input[type="tags"] {
	box-sizing: border-box;
}

.tags-container {
	display: none;
}

.TL-Input[type="tags"] .tags-container .selected-button {
	font-size: 12px;
	padding: 0.25em 0.4em;
	background-color: var(--Theme-Color);
	border-radius: 2px;
	color: white;
	padding-left: 0.4em;
}

[type="tags"] .tags-container.active {
	margin-top: 8px;
	padding: 0px 8px;
	display: flex;
	gap: 4px;
	flex-wrap: wrap;
	min-height: 21px;
	transition: min-height 150ms ease;
}
/*
**
**
**
**
**
**
** Input Button
** =================================
*/

button.TL-Button.TL-Input-Button {
	width: 100%;
	height: 44px;
	border-radius: 30px;
}

/*
**
**
**
**
**
**
** File Upload Input
** =================================
*/

button.TL-Submit-Popup-Form {
	margin: 16px 0 0 0;
	width: 100%;
	height: 46px;
}

/* Dark mode icon adjustments for date/time pickers (placed at EOF to avoid nesting issues) */
body[data-theme="dark"] .TL-Date-Picker > .selection-module > .selection-editor > button > img,
body[data-theme="dark"] .TL-Time-Picker > .selection-module > .selection-editor > button > img {
	filter: invert(1) brightness(1.2);
	opacity: 0.9;
}
