@import "reset.css";

/*
**
**
**
**
**
** Declarations
** =================================
*/

:root {
	/* Light theme variables */
	--background-primary: #ffffff;
	--background-secondary: #f0f0f2;
	--background-tertiary: #fafafa;
	--text-primary: #181818;
	--text-secondary: #666666;
	--text-tertiary: #999999;
	--border-color: #e0e0e0;
	--border-color-light: #f0f0f0;
	--shadow-color: rgba(0, 0, 0, 0.1);
	--overlay-color: rgba(255, 255, 255, 0.9);

	/* Existing theme colors */
	--Backdrop-Color: #f0f0f2;
	--Theme-Color: #6052ff;
	--Theme-Color-Light: #786cff;
	--Theme-Color-Dark: #2a1dbd;
	--Theme-Gradient: linear-gradient(120deg, #786cff, #2a1dbd);
	--Frosted-Glass-Backdrop: linear-gradient(120deg, #eff6fb, #e0e7f3, #f3e0e0, #eff6fb);

	/* Scrollbar (Light defaults) */
	--scrollbar-track: #f5f5f5; /* gutter/track background */
	--scrollbar-thumb: #a8a8ad; /* thumb color */
	--scrollbar-thumb-hover: #b5b5ba;
	--scrollbar-thumb-active: #9f9fa5;

	/* Popup (Content Window) - Light defaults */
	--Popup-Backdrop-Color: rgba(10, 10, 10, 0.6);
	--Popup-Surface-Color: #f8f8f8;
	--Popup-Text-Color: var(--text-primary);
	--Popup-Header-Border-Color: #d0d0d0;
	--Popup-Shadow: 0 8px 22px -1px rgba(0, 0, 0, 0.32);
}

/* Dark theme variables */
body[data-theme="dark"] {
	--background-primary: #1a1a1a;
	--background-secondary: #2a2a2a;
	--background-tertiary: #333333;
	--text-primary: #f5f5f5;
	--text-secondary: #a3a3a3;
	--text-tertiary: #666666;
	--border-color: #404040;
	--border-color-light: #525252;
	--shadow-color: rgba(0, 0, 0, 0.3);

	/* Scrollbar (Dark defaults) - slightly lighter per request */
	--scrollbar-track: #141414;
	--scrollbar-thumb: #4a4a4a;
	--scrollbar-thumb-hover: #5a5a5a;

	/* Popup (Content Window) - Dark overrides */
	--Popup-Backdrop-Color: rgba(0, 0, 0, 0.6);
	--Popup-Surface-Color: #1f1f1f;
	--Popup-Text-Color: #f5f5f5;
	--Popup-Header-Border-Color: #404040;
	--Popup-Shadow: 0 8px 22px -1px rgba(0, 0, 0, 0.6);

	--scrollbar-thumb-active: #6a6a6a;

	--overlay-color: rgba(0, 0, 0, 0.8);

	/* Override existing variables for dark mode */
	--Backdrop-Color: #18181a;
	--Frosted-Glass-Backdrop: linear-gradient(120deg, #2a2a2a, #1a1a1a, #2a2a2a, #1a1a1a);
}
/* Dark mode scrollbar colors for site wide scrollbars */
html[data-theme="dark"] {
	--scrollbar-track: #141414;
	--scrollbar-thumb: #4a4a4a;
	--scrollbar-thumb-hover: #5a5a5a;
	--scrollbar-thumb-active: #6a6a6a;
}

/*
**
**
**
**
**
** App Wrapper
** =================================
*/

html,
body {
	width: 100%;
	height: 100%;
	padding: 0;
	overflow: hidden;
}
body {
	box-sizing: border-box;
	overflow: auto;
	overflow-y: overlay;
}

#TL-App {
	position: relative;
	min-height: 100%;
}

/*
**
**
**
**
**
** Content Toggle
** =================================
*/

.TL-Content-Toggle {
	position: relative;
	margin: 12px auto;
	width: 100%;
	height: 30px;
	padding: 6px;
	overflow: hidden;
	text-align: center;
}
.TL-Content-Toggle > button {
	display: inline-block;
	margin: 0;
	vertical-align: top;
	width: auto;
	height: 100%;
	padding: 0 12px;
	background-color: #e8e8ea;
	font-size: 12.5px;
	color: #28282a;
	font-weight: 400;
	letter-spacing: 0;
	line-height: 16px;
}
.TL-Content-Toggle > button:first-child {
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
}
.TL-Content-Toggle > button:last-child {
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
}
.TL-Content-Toggle > button.active {
	z-index: 5;
	background-color: #fff;
	font-weight: 500;
	color: #181818;
	border-radius: 3px;
	box-shadow: 0 2px 3px -1px rgba(20, 20, 22, 0.28);
}

/*
**
**
**
**
**
** Context Menu
** =================================
*/

.TL-Context-Menu {
	position: fixed;
	z-index: 30;
	top: 0;
	left: 0;
	width: 0;
	height: auto;
	padding: 0;
	/* background-color: rgba(40, 40, 46, 0.96); */
	background-color: rgba(255, 255, 255, 1);
	overflow: hidden;
	border-radius: 12px;
	/* box-shadow: 0 12px 32px 0 rgba(20, 20, 22, 0.42); */
	box-shadow: 0 12px 32px 0 rgba(20, 20, 20, 0.32);
}
.TL-Context-Menu.active {
	width: 180px;
}
.TL-Context-Menu > ul > li {
	position: relative;
	margin-top: -50px;
	width: 94%;
	height: 30px;
	padding: 15px 0 0 6%;
	background-color: transparent;
	background-repeat: no-repeat;
	background-size: 16px auto;
	background-position: calc(100% - 14px) center;
	/*border-bottom: 1px solid #424244;*/
	border-bottom: 1px solid #ededed;
	opacity: 0;
	text-align: left;
	cursor: pointer;
	font-size: 13px;
	/*color: #c8c8ca;*/
	color: #181818;
	font-weight: 400;
	letter-spacing: 0;
	line-height: 15px;
	-webkit-transition: margin-top 280ms cubic-bezier(0.32, 0.22, 0.08, 1.22), opacity 120ms ease;
	-moz-transition: margin-top 280ms cubic-bezier(0.32, 0.22, 0.08, 1.22), opacity 120ms ease;
	transition: margin-top 280ms cubic-bezier(0.32, 0.22, 0.08, 1.22), opacity 120ms ease;
}
.TL-Context-Menu > ul > li > span {
	display: inline-block;
	vertical-align: top;
	width: calc(100% - 40px);
}
.TL-Context-Menu > ul > li > img {
	position: absolute;
	top: 0;
	bottom: 0;
	right: 10px;
	margin: auto 0;
	width: 16px;
	height: auto;
}
.TL-Context-Menu > ul > li:hover {
	background-color: #505052;
	background-color: #f0f0f0;
}
.TL-Context-Menu.active > ul > li {
	margin-top: 0;
	opacity: 1;
}
.TL-Context-Menu.active > ul > li[disabled="true"] {
	opacity: 0.5;
	pointer-events: none;
}

/*
**
**
**
**
**
** Scroll Bars
** =================================
*/

html *::-webkit-scrollbar-track {
	background-color: transparent;
}
html *::-webkit-scrollbar {
	width: 8px;
	height: 8px;
	background-color: var(--scrollbar-track);
}
html *::-webkit-scrollbar-thumb {
	background-color: var(--scrollbar-thumb);
	border-radius: 3px;
}

/* Dark Mode: Global Scrollbar Colors */
html[data-theme="dark"]::-webkit-scrollbar-track,
body[data-theme="dark"]::-webkit-scrollbar-track,
body[data-theme="dark"] *::-webkit-scrollbar-track {
	background-color: var(--scrollbar-track);
}
html[data-theme="dark"]::-webkit-scrollbar,
body[data-theme="dark"]::-webkit-scrollbar,
body[data-theme="dark"] *::-webkit-scrollbar {
	background-color: var(--scrollbar-track);
}
html[data-theme="dark"]::-webkit-scrollbar-thumb,
body[data-theme="dark"]::-webkit-scrollbar-thumb,
body[data-theme="dark"] *::-webkit-scrollbar-thumb {
	background-color: var(--scrollbar-thumb);
}
html[data-theme="dark"]::-webkit-scrollbar-thumb:hover,
body[data-theme="dark"]::-webkit-scrollbar-thumb:hover,
body[data-theme="dark"] *::-webkit-scrollbar-thumb:hover {
	background-color: var(--scrollbar-thumb-hover);
}
html[data-theme="dark"]::-webkit-scrollbar-thumb:active,
body[data-theme="dark"]::-webkit-scrollbar-thumb:active,
body[data-theme="dark"] *::-webkit-scrollbar-thumb:active {
	background-color: var(--scrollbar-thumb-active);
}

/* Firefox (Dark Mode) */
html[data-theme="dark"],
body[data-theme="dark"],
body[data-theme="dark"] * {
	scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track); /* thumb color, track color */
}

/*
**
**
**
**
**
** Ellipsis
** =================================
*/

.TL-Ellipsis {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

/*
**
**
**
**
**
** User Photo
** =================================
*/

[user-photo] {
	position: relative;

	background-color: transparent;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
	border-radius: 100%;
}
.TL-Photo-Initials {
	position: relative;
	width: 100%;
	height: 100%;
	background: linear-gradient(120deg, #bec7d8, #747f93);
	text-align: center;
	text-transform: uppercase;
	font-family: var(--Theme-Font);
	font-weight: 500;
	color: #fff;
	letter-spacing: 0.4px;
	text-shadow: 0 1px 2px rgba(10, 10, 10, 0.2);
	border-radius: 100%;
}

.TL-Photo-Initials[data-color="grey"],
[user-color-filled="grey"] {
	background: linear-gradient(160deg, #bec7d8, #747f93);
}
.TL-Photo-Initials[data-color="blue"],
[user-color-filled="blue"] {
	background: linear-gradient(160deg, #77adff, #3a70c2);
}
.TL-Photo-Initials[data-color="green"],
[user-color-filled="green"] {
	background: linear-gradient(160deg, #61e8ab, #1e9860);
}
.TL-Photo-Initials[data-color="yellow"],
[user-color-filled="yellow"] {
	background: linear-gradient(160deg, #f1e77e, #d8b61a);
}
.TL-Photo-Initials[data-color="red"],
[user-color-filled="red"] {
	background: linear-gradient(160deg, #f06f5b, #c12c2c);
}
.TL-Photo-Initials[data-color="orange"],
[user-color-filled="orange"] {
	background: linear-gradient(160deg, #f8a067, #c8601c);
}
.TL-Photo-Initials[data-color="purple"],
[user-color-filled="purple"] {
	background: linear-gradient(160deg, #8985ff, #4945c1);
}
.TL-Photo-Initials[data-color="teal"],
[user-color-filled="teal"] {
	background: linear-gradient(160deg, #70def2, #38a2b5);
}
.TL-Photo-Initials[data-color="coral"],
[user-color-filled="coral"] {
	background: linear-gradient(160deg, #f3a18b, #e06053);
}
.TL-Photo-Initials[data-color="violet"],
[user-color-filled="violet"] {
	background: linear-gradient(160deg, #dc7aee, #a238b6);
}
.TL-Photo-Initials[data-color="farm"],
[user-color-filled="farm"] {
	background: linear-gradient(160deg, #f1e77e, #d8b61a);
}
/* Team Lyders User Override */
[user-photo="10"] > .TL-Photo-Initials {
	background: linear-gradient(140deg, #f797f7 0%, #50d0e0 100%);
}

/* User Backdrops */

[user-backdrop-filled="blue"] {
	background-image: url("../resources/users/backdrops/small/blue.webp");
}
[user-backdrop-filled="green"] {
	background-image: url("../resources/users/backdrops/small/green.webp");
}
[user-backdrop-filled="yellow"] {
	background-image: url("../resources/users/backdrops/small/yellow.webp");
}
[user-backdrop-filled="red"] {
	background-image: url("../resources/users/backdrops/small/red.webp");
}
[user-backdrop-filled="orange"] {
	background-image: url("../resources/users/backdrops/small/orange.webp");
}
[user-backdrop-filled="purple"] {
	background-image: url("../resources/users/backdrops/small/purple.webp");
}
[user-backdrop-filled="teal"] {
	background-image: url("../resources/users/backdrops/small/teal.webp");
}
[user-backdrop-filled="grey"] {
	background-image: url("../resources/users/backdrops/small/grey.webp");
}
[user-backdrop-filled="coral"] {
	background-image: url("../resources/users/backdrops/small/coral.webp");
}
[user-backdrop-filled="violet"] {
	background-image: url("../resources/users/backdrops/small/violet.webp");
}
[user-backdrop-filled="farm"] {
	background-image: url("../resources/users/backdrops/small/farm.webp");
}

/*
**
**
**
**
**
** User Photo Initials Brand Logo
** =================================
*/

.TL-Photo-Initials-Brand {
	position: absolute;
	top: -10px;
	right: -10px;
	width: 20px;
	height: 20px;
	background-color: #fff;
	/* border: 2px solid #fff; */
	text-align: center;
	overflow: hidden;
	border-radius: 100%;
}
.TL-Photo-Initials-Brand[title=""] {
	display: none;
}
.TL-Photo-Initials-Brand[title="TB-US"] {
	box-shadow: 0 2px 12px -1px rgba(110, 64, 200, 0.65);
}
.TL-Photo-Initials-Brand[title="RB-US"] {
	box-shadow: 0 2px 12px -1px rgba(154, 16, 30, 0.45);
}
.TL-Photo-Initials-Brand > img {
	/*
    If we want to have the photo be contained in the future:
    Set the width to a lower percentage to create padding (don't adjust height - keep at 100%)
    Set Object fit to contain
    */
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
}

/*
**
**
**
**
**
** Store Photo
** =================================
*/

[store-photo] {
	position: relative;
	overflow: hidden;
	background-color: transparent;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
}

/*
**
**
**
**
**
** Radial Loader
** ========================================
*/

.TL-Radial-Progress {
	position: absolute;
	top: 0;
	left: 0;
	width: auto;
	height: auto;
	padding: 0;
	margin: 0;
}
.TL-Radial-Progress .svg-wrap {
	width: 48px;
	height: 48px;
	background-color: transparent;
	-webkit-transform: rotate(-90deg);
	-moz-transform: rotate(-90deg);
	-ms-transform: rotate(-90deg);
	-o-transform: rotate(-90deg);
	transform: rotate(-90deg);
}
.TL-Radial-Progress .progress-cell,
.TL-Radial-Progress .progress-value {
	stroke-width: 11;
}
.TL-Radial-Progress .progress-cell {
	stroke: #f3c3b9;
}
.TL-Radial-Progress .progress-value {
	stroke: #4695ff;
	stroke-dasharray: 339.292;
	stroke-dashoffset: 339.292;
	-webkit-transition: stroke-dashoffset 1100ms ease;
	-moz-transition: stroke-dashoffset 1100ms ease;
	-ms-transition: stroke-dashoffset 1100ms ease;
	-o-transition: stroke-dashoffset 1100ms ease;
	transition: stroke-dashoffset 1100ms ease;
}

/*
**
**
**
**
**
** Key Value Pairs
** =================================
*/

ul.TL-Key-Value-List {
	position: relative;
	width: 100%;
	height: auto;
	text-align: left;
}
ul.TL-Key-Value-List > li {
	display: inline-block;
	position: relative;
	width: 100%;
	height: auto;
	padding: 0 0 6px 0;
}
ul.TL-Key-Value-List > li > .key {
	font-size: 14px;
	font-weight: 400;
	color: #484848;
	line-height: 18px;
}
ul.TL-Key-Value-List > li > .value {
	font-size: 18px;
	font-weight: 500;
	color: #181818;
	line-height: 22px;
}

/*
**
**
**
**
**
** Sequential Delay
** ========================================
*/

.TL-Sequential-Delay:nth-child(2) {
	transition-delay: 30ms !important;
}
.TL-Sequential-Delay:nth-child(3) {
	transition-delay: 60ms !important;
}
.TL-Sequential-Delay:nth-child(4) {
	transition-delay: 90ms !important;
}
.TL-Sequential-Delay:nth-child(5) {
	transition-delay: 120ms !important;
}
.TL-Sequential-Delay:nth-child(6) {
	transition-delay: 150ms !important;
}
.TL-Sequential-Delay:nth-child(7) {
	transition-delay: 180ms !important;
}
.TL-Sequential-Delay:nth-child(8) {
	transition-delay: 210ms !important;
}
.TL-Sequential-Delay:nth-child(9) {
	transition-delay: 240ms !important;
}
.TL-Sequential-Delay:nth-child(10) {
	transition-delay: 270ms !important;
}
.TL-Sequential-Delay:nth-child(11) {
	transition-delay: 300ms !important;
}
.TL-Sequential-Delay:nth-child(12) {
	transition-delay: 230ms !important;
}
.TL-Sequential-Delay:nth-child(13) {
	transition-delay: 360ms !important;
}
.TL-Sequential-Delay:nth-child(14) {
	transition-delay: 390ms !important;
}
.TL-Sequential-Delay:nth-child(15) {
	transition-delay: 420ms !important;
}
.TL-Sequential-Delay:nth-child(16) {
	transition-delay: 450ms !important;
}
.TL-Sequential-Delay:nth-child(17) {
	transition-delay: 480ms !important;
}
.TL-Sequential-Delay:nth-child(18) {
	transition-delay: 510ms !important;
}
.TL-Sequential-Delay:nth-child(19) {
	transition-delay: 540ms !important;
}
.TL-Sequential-Delay:nth-child(20) {
	transition-delay: 570ms !important;
}
.TL-Sequential-Delay:nth-child(n + 20) {
	transition-delay: 600ms !important;
}

/*
**
**
**
**
**
** Animations
** =================================
*/

@keyframes RotateCountClockWise {
	100% {
		transform: rotate(-360deg);
	}
}

@keyframes Wiggle {
	0% {
		transform: rotate(0deg);
	}
	25% {
		transform: rotate(8deg);
	}
	50% {
		transform: rotate(-8deg);
	}
	100% {
		transform: rotate(0deg);
	}
}

@keyframes ImportantAlert {
	0% {
		background-color: #d8d8da;
	}
	50% {
		background-color: #fd3930;
		box-shadow: 0 2px 12px 0 rgba(120, 30, 30, 0.52);
	}
	100% {
		background-color: #d8d8da;
	}
}

/*
**
**
**
**
**
** Loading
** =================================
*/

.TL-Loading {
	position: absolute;
	z-index: 100;
	top: 0;
	left: 0;
	margin: 0;
	width: 100%;
	height: 100%;
	padding: 0;
	background-color: rgba(0, 0, 0, 0.35);
	text-align: center;
	opacity: 0;
	-webkit-transition: opacity 400ms ease;
	-moz-transition: opacity 400ms ease;
	transition: opacity 400ms ease;
}
.TL-Loading.transition-in {
	opacity: 1;
}
main > .TL-Loading {
	position: fixed;
}
.TL-Loading > figure {
	position: absolute;
	margin: auto;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	width: 20%;
	max-width: 42px;
	height: 100%;
	max-height: 42px;
}
.TL-Loading > figure > img {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	width: 100%;
	height: auto;
	opacity: 1;
	transform: scale(0);
	-webkit-transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.22);
	-moz-transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.22);
	transition: transform 280ms cubic-bezier(0.32, 0.22, 0.08, 1.22);
}
.TL-Loading.transition-in > figure > img {
	transform: scale(1);
}
.TL-Loading-Disabled-Parent {
	pointer-events: none !important;
}
/*
**
**
**
**
**
** Universal Button
** ========================================
*/

button.TL-Button {
	display: inline-block;
	margin: 0;
	vertical-align: top;
	width: auto;
	height: auto;
	min-height: 38px;
	padding: 2px 10px;
	background: linear-gradient(-4deg, #939cff, #896ef5);
	text-align: center;
	font-size: 14px;
	color: #fff;
	font-weight: 500;
	letter-spacing: 0.2px;
	border-radius: 12px;
	box-shadow: 0 1px 2px 0 rgba(30, 10, 100, 0.24);
	transition: transform 120ms ease;
}
button.TL-Button[theme="red"] {
	background: linear-gradient(-4deg, #ee5a4b, #bb2e20);
}
button.TL-Button[theme="green"] {
	background: linear-gradient(-4deg, #51ce82, #08965e);
}
button.TL-Button[theme="slate"] {
	background: linear-gradient(-4deg, #9db9ca, #567386);
}
button.TL-Button:active {
	transform: scale(0.98);
}

/*
**
**
**
**
**
** Popup Content Window
** ========================================
*/

.TL-Content-Window-Wrapper {
	display: none;
	position: fixed;
	box-sizing: border-box;
	z-index: 25;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	width: 100%;
	height: 100%;
	padding: 150px 0;
	background-color: var(--Popup-Backdrop-Color);
	overflow: auto;
}
.TL-Content-Window-Wrapper[backdrop-blur="true"] {
	backdrop-filter: blur(4px);
}

/* Content Window */

.TL-Content-Window-Wrapper > .content-window {
	position: relative;
	margin: 0 auto;
	width: 94%;
	max-width: 600px;
	height: auto;
	padding: 12px 0;
	background-color: var(--Popup-Surface-Color);
	border-radius: 12px;
	box-shadow: var(--Popup-Shadow);
	opacity: 0;
	-webkit-transform: translateY(50px) scale(0.96);
	-moz-transform: translateY(50px) scale(0.96);
	transform: translateY(50px) scale(0.96);
	-webkit-transition: transform 420ms cubic-bezier(0.28, 0.28, 0.08, 1.12), opacity 180ms ease;
	-moz-transition: transform 420ms cubic-bezier(0.28, 0.28, 0.08, 1.12), opacity 180ms ease;
	transition: transform 420ms cubic-bezier(0.28, 0.28, 0.08, 1.12), opacity 180ms ease;
}
.TL-Content-Window-Wrapper[window-size="small"] > .content-window {
	max-width: 432px;
}
.TL-Content-Window-Wrapper[window-size="medium"] > .content-window {
	max-width: 532px;
}
.TL-Content-Window-Wrapper[window-size="large"] > .content-window {
	max-width: 632px;
}
.TL-Content-Window-Wrapper[window-size="mega"] > .content-window {
	max-width: 732px;
}
.TL-Content-Window-Wrapper.active > .content-window {
	opacity: 1;
	-webkit-transform: translateY(0) scale(1);
	-moz-transform: translateY(0) scale(1);
	transform: translateY(0) scale(1);
}

/* Content Window Header */

.TL-Content-Window-Wrapper > .content-window > header {
	position: relative;
	box-sizing: border-box;
	margin: 0 auto;
	width: 92%;
	height: auto;
	padding: 0 0 12px 34px;
	background-color: transparent;
	border-bottom: 1px solid var(--Popup-Header-Border-Color);
	text-align: left;
}
.TL-Content-Window-Wrapper > .content-window > header > img {
	position: absolute;
	top: 4px;
	left: -2px;
	width: 32px;
	height: auto;
}
.TL-Content-Window-Wrapper > .content-window > header > h1 {
	display: inline-block;
	width: calc(100% - 90px);
	padding: 6px 0 0 0;
	text-align: left;
	font-family: var(--Theme-Font);
	font-size: 22px;
	color: var(--Popup-Text-Color);
	font-weight: 600;
	line-height: 26px;
}
.TL-Content-Window-Wrapper > .content-window > header > button.close-content-window {
	display: block;
	position: absolute;
	top: 6px;
	right: 0;
	margin: 0;
	width: 78px;
	height: 28px;
	padding: 0 18px 0 0;
	background: transparent;
	text-align: right;
	font-size: 18px;
	color: var(--Popup-Text-Color);
	font-weight: 500;
	line-height: 20px;
}
.TL-Content-Window-Wrapper > .content-window > header > button.close-content-window > img {
	position: absolute;
	top: 4px;
	right: 0;
	width: 20px;
	height: auto;
}

/* Dark mode icon adjustments */
body[data-theme="dark"] .TL-Content-Window-Wrapper > .content-window > header > button.close-content-window > img {
	filter: invert(1) brightness(1.2);
	opacity: 0.9;
}

/* General page stylings */

.TL-Page-Block {
	border: 1px solid #ddd;
	border: 1px solid #ddd;
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: auto;
	padding: 16px;
	background-color: #fbfbfc;
	border-radius: 16px;
	box-shadow: 0 12px 28px -4px rgba(10, 10, 10, 0.18);
}

.TL .TL-Page-Block > header {
	font-family: var(--Theme-Font);
	font-size: 2em;
	font-weight: 600;
}

.TL-Card-Divider {
	border: 0;
	border-bottom: 1px solid #ddd;
}

/* Content HTML Wrapper */

.TL-Content-Window-Wrapper > .content-window > .content {
	position: relative;
	margin: 0 auto;
	width: 92%;
	height: auto;
	padding: 12px 0 22px 0;
}

/*
**
**
**
**
**
** TL Timeline
** ========================================
*/

.TL-Timeline {
	position: relative;
	margin: 0;
	width: 100%;
	height: auto;
}

.TL-Timeline .TL-Timeline-Event {
	position: relative;
	box-sizing: border-box;
	margin: 0 0 62px 0;
	width: 100%;
	height: auto;
	padding: 0 0 0 50px;
	background-color: transparent;
	text-align: left;
	opacity: 0;
	-webkit-transform: translateY(50px);
	-moz-transform: translateY(50px);
	transform: translateY(50px);
	-webkit-transition: -webkit-transform 580ms cubic-bezier(0.42, 0.12, 0, 1.38), opacity 580ms ease;
	-moz-transition: -moz-transform 580ms cubic-bezier(0.42, 0.12, 0, 1.38), opacity 580ms ease;
	transition: transform 580ms cubic-bezier(0.42, 0.12, 0, 1.38), opacity 580ms ease;
}
.TL-Timeline.active .TL-Timeline-Event {
	opacity: 1;
	-webkit-transform: translateY(0);
	-moz-transform: translateY(0);
	transform: translateY(0);
}
.TL-Timeline .TL-Timeline-Event:first-child::before,
.TL-Timeline .TL-Timeline-Event::after {
	content: "";
	display: block;
	position: absolute;
	left: 18px;
	top: 52px;
	width: 4px;
	height: 100%;
	background-color: #c2c2c4;
	border-radius: 30px;
}
.TL-Timeline .TL-Timeline-Event:first-child::before {
	top: -32px;
	height: 22px;
}
.TL-Timeline .TL-Timeline-Event:last-child::after {
	/*display:none;*/
}
.TL-Timeline .TL-Timeline-Event > .date-indicator {
	position: absolute;
	z-index: 3;
	top: 2px;
	left: 0;
	margin: 0;
	box-sizing: border-box;
	width: 38px;
	height: 38px;
	padding: 5px 0 0 0;
	background-color: #fff;
	text-align: center;
	border-radius: 8px;
	box-shadow: 0 1px 3px 0 rgba(20, 20, 22, 0.18);
}
.TL-Timeline .TL-Timeline-Event > .date-indicator > img {
	width: 28px;
	height: auto;
}
.TL-Timeline .TL-Timeline-Event > header {
	position: relative;
	margin: 0;
	width: 100%;
}
.TL-Timeline .TL-Timeline-Event > header > h2.date-time {
	display: inline-block;
	width: 100%;
	font-size: 14px;
	color: #68686a;
	font-weight: 500;
	line-height: 18px;
}
.TL-Timeline .TL-Timeline-Event > header > h2.date-time > span.year {
	font-weight: 400;
}
.TL-Timeline .TL-Timeline-Event > header > h3.sub-title {
	display: inline-block;
	width: 100%;
	font-size: 16px;
	color: #4c75ff;
	font-weight: 500;
	line-height: 18px;
}
.TL-Timeline .TL-Timeline-Event > header > h1.event-type {
	display: inline-block;
	width: 100%;
	font-size: 22px;
	color: #28282a;
	font-weight: 700;
	line-height: 24px;
}
.TL-Timeline .TL-Timeline-Event > .event-detail {
	position: relative;
	margin: 0;
	width: 100%;
	padding: 8px 0;
}
.TL-Timeline .TL-Timeline-Event > .event-detail > p {
	font-size: 13.5px;
	color: #28282a;
	font-weight: 400;
	line-height: 20px;
}
.TL-Timeline .TL-Timeline-Event > .event-detail > p > b {
	font-weight: 500;
	color: #000;
}

/*
**
**
**
**
**
** Mobile Support Message
** ========================================
*/

#TL-Mobile-Support {
	display: none;
}

/*
**
**
**
**
**
** Browser Support Message
** ========================================
*/

#TL-Browser-Support {
	display: none;
}

/*
**
**
**
**
**
** Rich Text Output
** ========================================
*/
.TL-Rich-Text-Output h1,
.TL-Rich-Text-Output h2,
.TL-Rich-Text-Output h3,
.TL-Rich-Text-Output h4,
.TL-Rich-Text-Output h5,
.TL-Rich-Text-Output h6 {
	margin-top: 10px;
	margin-bottom: 10px;
	font-weight: 500;
}

.TL-Rich-Text-Output p,
.TL-Rich-Text-Output span,
.TL-Rich-Text-Output a {
	line-height: 1.5;
	font-size: 15px;
}

.TL-Rich-Text-Output h1 {
	font-size: 30px;
}

.TL-Rich-Text-Output h2 {
	font-size: 24px;
}

.TL-Rich-Text-Output h3 {
	font-size: 16px;
}

.TL-Rich-Text-Output li,
.TL-Rich-Text-Output ol,
.TL-Rich-Text-Output ul {
	list-style-type: initial !important;
	text-decoration: initial !important;
}

.TL-Rich-Text-Output li,
.TL-Rich-Text-Output ol {
	line-height: 1.5;
	display: block;
	position: relative;
	font-size: 15px;
}

.TL-Rich-Text-Output a {
	text-decoration: underline;
	text-underline-offset: 3px;
	color: var(--Rich-Text-Link-Color) !important;
}
